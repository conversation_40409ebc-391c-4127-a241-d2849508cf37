from PyQt5.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QCheckBox, QFrame,
    QFileDialog, QMessageBox, QComboBox
)
import json
from pathlib import Path
import os

# Find the MediaSorter root directory using the same logic as other files
def find_mediasorter_root():
    """
    Find the MediaSorter root directory by looking for the characteristic folder structure.
    This makes the application portable regardless of the main folder name or location.
    """
    current = Path(__file__).resolve().parent.parent  # Start from the Brain folder

    # Check if we're already in the Brain folder (0.3 Brain Folder)
    if current.name == "0.3 Brain Folder":
        return current.parent

    # Look for the Brain folder in the current directory or parent directories
    while current.parent != current:  # Stop at filesystem root
        # Check if this directory contains the Brain folder
        brain_folder = current / "0.3 Brain Folder"
        if brain_folder.exists() and brain_folder.is_dir():
            return current
        current = current.parent

    # If we can't find the characteristic structure, use the parent of the current file's directory
    # This ensures the app works even if the folder structure is incomplete
    return Path(__file__).resolve().parent.parent.parent

# Update settings file path to be in Brain folder
brain_folder = Path(__file__).resolve().parent.parent
settings_folder = brain_folder / "settings"
settings_file = settings_folder / "user_settings.json"

def load_settings():
    if settings_file.exists():
        try:
            with open(settings_file, "r", encoding="utf-8") as f:
                settings = json.load(f)
                # Ensure custom folders exist but don't add defaults
                if "custom_folders" not in settings:
                    settings["custom_folders"] = []
                else:
                    # Ensure all existing folders have the correct sort_type format
                    for folder in settings["custom_folders"]:
                        if "sort_type" not in folder or not isinstance(folder["sort_type"], str):
                            folder["sort_type"] = "Other Files"
                return settings
        except Exception as e:
            print(f"Error loading settings: {e}")
            return get_default_settings()
    return get_default_settings()

def save_settings(data):
    try:
        settings_file.parent.mkdir(parents=True, exist_ok=True)
        with open(settings_file, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4)
        return True
    except Exception as e:
        print(f"Error saving settings: {e}")
        return False

def get_default_settings():
    """Return default settings with proper folder structure"""
    # Use the portable root finding function
    root_folder = find_mediasorter_root()

    return {
        "season_sort_path": "",
        "movie_sort_path": "",
        "use_season_folder": False,
        "use_movie_folder": False,
        "create_movie_metadata": True,  # Default to creating metadata files
        "custom_folders": [
            {
                "name": "TV Shows",
                "path": str(root_folder / "1.0 TV Shows"),
                "enabled": True,
                "sort_type": "TV Shows"
            },
            {
                "name": "Movies",
                "path": str(root_folder / "2.0 Movies"),
                "enabled": True,
                "sort_type": "Movies"
            },
            {
                "name": "Unsorted",
                "path": str(root_folder / "0.2 Unsorted Folder"),
                "enabled": True,
                "sort_type": "Other Files"
            }
        ]
    }

class FolderSettingWidget(QWidget):
    def __init__(self, folder_data, settings, parent=None):
        super().__init__(parent)
        self.folder_data = folder_data
        self.settings = settings

        layout = QVBoxLayout()
        self.setLayout(layout)

        # Create frame
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame_layout = QVBoxLayout()
        frame.setLayout(frame_layout)

        # Add controls
        header_layout = QHBoxLayout()
        self.use_toggle = QCheckBox()
        self.use_toggle.setChecked(folder_data.get("enabled", False))
        header_layout.addWidget(self.use_toggle)

        self.name_input = QLineEdit(folder_data.get("name", ""))
        header_layout.addWidget(self.name_input)

        # Add delete button
        delete_btn = QPushButton("🗑️")
        delete_btn.setToolTip("Delete this sort destination")
        delete_btn.setFixedWidth(30)
        delete_btn.clicked.connect(self.delete_folder)
        header_layout.addWidget(delete_btn)

        frame_layout.addLayout(header_layout)

        path_layout = QHBoxLayout()
        self.path_input = QLineEdit(folder_data.get("path", ""))
        path_layout.addWidget(self.path_input)

        browse_btn = QPushButton("Browse")
        path_layout.addWidget(browse_btn)
        frame_layout.addLayout(path_layout)

        # Add type selection
        type_layout = QHBoxLayout()
        type_label = QLabel("Sort Type:")
        self.type_combo = QComboBox()
        self.type_combo.addItems(["TV Shows", "Movies", "Other Files"])
        self.type_combo.setCurrentText(folder_data.get("sort_type", "Other Files"))
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.type_combo)
        frame_layout.addLayout(type_layout)

        layout.addWidget(frame)

        # Connect signals
        self.use_toggle.stateChanged.connect(self.update_enabled)
        self.name_input.editingFinished.connect(self.update_name)
        self.path_input.editingFinished.connect(self.update_path)
        browse_btn.clicked.connect(self.browse_path)
        self.type_combo.currentTextChanged.connect(self.update_type)

        # Initial appearance
        self.update_appearance()

    def update_appearance(self):
        """Update widget appearance based on enabled state"""
        is_enabled = self.folder_data["enabled"]

        # Define colors based on theme
        if self.palette().color(self.backgroundRole()).lightness() > 128:
            # Light theme
            enabled_bg = "#ffffff"
            enabled_text = "#000000"
            disabled_bg = "#f0f0f0"
            disabled_text = "#666666"
            border_color = "#cccccc"
        else:
            # Dark theme
            enabled_bg = "#353535"
            enabled_text = "#ffffff"
            disabled_bg = "#2d2d2d"
            disabled_text = "#808080"
            border_color = "#404040"

        # Set widget styles
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {enabled_bg if is_enabled else disabled_bg};
                color: {enabled_text if is_enabled else disabled_text};
                border: 1px solid {border_color};
                border-radius: 4px;
                padding: 8px;
            }}
            QLineEdit {{
                background-color: {enabled_bg};
                color: {enabled_text};
                border: 1px solid {border_color};
                border-radius: 4px;
                padding: 4px;
            }}
            QLineEdit:disabled {{
                background-color: {disabled_bg};
                color: {disabled_text};
            }}
            QPushButton {{
                background-color: {'#0078D4' if is_enabled else disabled_bg};
                color: {'white' if is_enabled else disabled_text};
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {'#1084D9' if is_enabled else disabled_bg};
            }}
            QPushButton:pressed {{
                background-color: {'#006CBD' if is_enabled else disabled_bg};
            }}
            QCheckBox {{
                color: {enabled_text};
                background: transparent;
            }}
        """)

        # Enable/disable input fields
        self.name_input.setEnabled(is_enabled)
        self.path_input.setEnabled(is_enabled)

    def browse_path(self):
        path = QFileDialog.getExistingDirectory(self, f"Select folder for {self.folder_data['name']}")
        if path:
            self.path_input.setText(path)
            self.update_path()

    def update_enabled(self):
        self.folder_data["enabled"] = self.use_toggle.isChecked()
        save_settings(self.settings)
        self.update_appearance()

        # Notify user
        if self.use_toggle.isChecked():
            QMessageBox.information(self, "Folder Activated",
                f"'{self.folder_data['name']}' is now active for {self.type_combo.currentText()} sorting.")

    def update_name(self):
        new_name = self.name_input.text().strip()
        if new_name:
            self.folder_data["name"] = new_name
            save_settings(self.settings)

    def update_path(self):
        path = self.path_input.text().strip()
        if path:
            if os.path.exists(path):
                self.folder_data["path"] = path
                save_settings(self.settings)
                if self.folder_data["enabled"]:
                    QMessageBox.information(self, "Path Updated",
                        f"Files will now be sorted to:\n{path}")
            else:
                QMessageBox.warning(self, "Invalid Path",
                    f"The path does not exist:\n{path}")
                self.path_input.setText(self.folder_data["path"])

    def update_type(self):
        self.folder_data["sort_type"] = self.type_combo.currentText()
        save_settings(self.settings)

    def delete_folder(self):
        """Delete this folder from settings and remove the widget"""
        reply = QMessageBox.question(
            self,
            "Confirm Delete",
            f"Are you sure you want to delete the sort destination '{self.folder_data['name']}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Remove from settings
            self.settings["custom_folders"].remove(self.folder_data)
            save_settings(self.settings)

            # Remove widget
            self.setParent(None)
            self.deleteLater()

            QMessageBox.information(
                self,
                "Folder Deleted",
                f"Sort destination '{self.folder_data['name']}' has been removed."
            )

def get_settings_widget():
    widget = QWidget()
    layout = QVBoxLayout()
    settings = load_settings()

    # Season Sort Path
    use_season_toggle = QCheckBox("Use this folder for season sorting")
    use_season_toggle.setChecked(settings.get("use_season_folder", False))

    season_layout = QHBoxLayout()
    season_label = QLabel("Season Sort Folder:")
    season_input = QLineEdit(settings.get("season_sort_path", ""))
    season_button = QPushButton("Browse")

    season_layout.addWidget(season_label)
    season_layout.addWidget(season_input)
    season_layout.addWidget(season_button)

    # Movie Sort Path
    use_movie_toggle = QCheckBox("Use this folder for movie sorting")
    use_movie_toggle.setChecked(settings.get("use_movie_folder", False))

    movie_layout = QHBoxLayout()
    movie_label = QLabel("Movie Sort Folder:")
    movie_input = QLineEdit(settings.get("movie_sort_path", ""))
    movie_button = QPushButton("Browse")

    movie_layout.addWidget(movie_label)
    movie_layout.addWidget(movie_input)
    movie_layout.addWidget(movie_button)

    # Movie Metadata Toggle
    create_metadata_toggle = QCheckBox("Create movie_info.json metadata files")
    create_metadata_toggle.setChecked(settings.get("create_movie_metadata", True))
    create_metadata_toggle.setToolTip("When enabled, creates metadata files for each movie. Disable to stop creating movie_info.json files.")

    # Add a frame for movie settings
    movie_settings_frame = QFrame()
    movie_settings_frame.setFrameShape(QFrame.StyledPanel)
    movie_settings_frame.setStyleSheet("QFrame { background-color: rgba(0, 0, 0, 0.05); border-radius: 5px; padding: 10px; }")
    movie_settings_layout = QVBoxLayout(movie_settings_frame)

    movie_settings_title = QLabel("Movie Settings")
    movie_settings_title.setStyleSheet("font-weight: bold;")
    movie_settings_layout.addWidget(movie_settings_title)
    movie_settings_layout.addWidget(create_metadata_toggle)

    # Add existing components
    layout.addWidget(use_season_toggle)
    layout.addLayout(season_layout)
    layout.addWidget(use_movie_toggle)
    layout.addLayout(movie_layout)
    layout.addWidget(movie_settings_frame)

    # Add separator
    separator = QLabel("Additional Sort Folders")
    separator.setStyleSheet("font-weight: bold; margin-top: 15px;")
    layout.addWidget(separator)

    # Add custom folder widgets
    custom_folders_layout = QVBoxLayout()
    for folder_data in settings["custom_folders"]:
        folder_widget = FolderSettingWidget(folder_data, settings)
        custom_folders_layout.addWidget(folder_widget)
    layout.addLayout(custom_folders_layout)

    # Event handlers for season and movie folders
    def set_season_path():
        path = QFileDialog.getExistingDirectory(widget, "Select Season Folder")
        if path:
            path_str = str(path)
            season_input.setText(path_str)
            settings["season_sort_path"] = path_str
            save_settings(settings)

    def update_use_season_toggle():
        settings["use_season_folder"] = use_season_toggle.isChecked()
        save_settings(settings)

    def update_season_path_manually():
        path = season_input.text().strip()
        if path and os.path.exists(path):
            settings["season_sort_path"] = path
            save_settings(settings)
        else:
            QMessageBox.warning(widget, "Invalid Path", f"The path does not exist:\n{path}")
            season_input.setText(settings["season_sort_path"])

    def set_movie_path():
        path = QFileDialog.getExistingDirectory(widget, "Select Movie Folder")
        if path:
            path_str = str(path)
            movie_input.setText(path_str)
            settings["movie_sort_path"] = path_str
            save_settings(settings)

    def update_use_movie_toggle():
        settings["use_movie_folder"] = use_movie_toggle.isChecked()
        save_settings(settings)

    def update_movie_path_manually():
        path = movie_input.text().strip()
        if path and os.path.exists(path):
            settings["movie_sort_path"] = path
            save_settings(settings)
        else:
            QMessageBox.warning(widget, "Invalid Path", f"The path does not exist:\n{path}")
            movie_input.setText(settings["movie_sort_path"])

    # Event handler for metadata toggle
    def update_metadata_toggle():
        settings["create_movie_metadata"] = create_metadata_toggle.isChecked()
        save_settings(settings)

        # Show confirmation message
        if create_metadata_toggle.isChecked():
            QMessageBox.information(
                widget,
                "Metadata Enabled",
                "Movie metadata files (movie_info.json) will be created when sorting movies."
            )
        else:
            QMessageBox.information(
                widget,
                "Metadata Disabled",
                "Movie metadata files (movie_info.json) will no longer be created when sorting movies."
            )

    # Connect signals
    season_button.clicked.connect(set_season_path)
    use_season_toggle.stateChanged.connect(update_use_season_toggle)
    season_input.editingFinished.connect(update_season_path_manually)

    movie_button.clicked.connect(set_movie_path)
    use_movie_toggle.stateChanged.connect(update_use_movie_toggle)
    movie_input.editingFinished.connect(update_movie_path_manually)
    create_metadata_toggle.stateChanged.connect(update_metadata_toggle)

    widget.setLayout(layout)
    return widget