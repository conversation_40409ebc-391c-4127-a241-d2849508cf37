# tv_show.py - always use correct sorting and unsorted folders
import os
import shutil
import logging
from pathlib import Path
import re
from time import sleep
from typing import Union
from datetime import datetime
try:
    from .tv_show_destinations import get_tv_destinations
except ImportError:
    # Fallback for when module is imported directly
    from tv_show_destinations import get_tv_destinations

def find_mediasorter_root():
    """
    Find the MediaSorter root directory. Since we're in the Brain folder,
    the root is simply two levels up from this file.
    """
    # We're in tv_show_code folder inside Brain folder, so root is two levels up
    return Path(__file__).resolve().parent.parent.parent

# Configure logging
main_folder = find_mediasorter_root()
log_dir = main_folder / '0.3 Brain Folder' / 'logs'
log_dir.mkdir(parents=True, exist_ok=True)

log_file = log_dir / 'tv_show_log_3.txt'
logging.basicConfig(
    filename=str(log_file),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

sorting_folder = main_folder / "0.1 Sorting Folder"
unsorted_folder = main_folder / "0.2 Unsorted Folder"

# Ensure necessary folders exist
for folder in [sorting_folder, unsorted_folder]:
    folder.mkdir(parents=True, exist_ok=True)

logging.info("Folders verified.")

def process_files(file_paths):
    """Process a list of file paths and sort them."""
    # Check for enabled custom TV Show Sort Destination
    destinations = get_tv_destinations()
    custom_sort_folder = None
    for dest in destinations:
        if dest.get("enabled") and dest.get("path"):
            custom_sort_folder = Path(dest["path"])
            break
    if custom_sort_folder:
        sorting_root = custom_sort_folder
    else:
        sorting_root = sorting_folder

    total_files = len(file_paths)
    sorted_files = 0
    unsorted_files = 0

    print("Starting file processing...")

    for idx, file_path in enumerate(file_paths, start=1):
        file = Path(file_path)
        try:
            if not file.is_file():
                logging.warning(f"Skipped: {file} is not a file.")
                print(f"[{idx}/{total_files}] Skipped: {file.name} is not a file.")
                continue

            logging.info(f"Processing file: {file.name}")
            print(f"[{idx}/{total_files}] Processing: {file.name}")
            sleep(0.1)

            # Try standard S01E01 or Season/Episode format
            match = re.match(r"(.+?)[\s._-]*(?:S(\d{2})[\s._-]*E(\d{2})|Season[\s]*(\d+)[\s]*Episode[\s]*(\d+))", file.name, re.IGNORECASE)
            season = episode = None

            if match:
                series_name = match.group(1)
                season = match.group(2) or match.group(4)
                episode = match.group(3) or match.group(5)
            else:
                # Try to catch 'Episode 3' with no season info
                match_alt = re.match(r"(.+?)[\s._-]*Episode[\s]*(\d{1,3})", file.name, re.IGNORECASE)
                if match_alt:
                    series_name = match_alt.group(1)
                    episode = match_alt.group(2)
                    season = "01"  # Default season to 01

            if match or ("match_alt" in locals() and match_alt):
                # Normalize name
                series_name = re.sub(r"[._-]+", " ", series_name).strip()
                season = season.zfill(2)
                episode = episode.zfill(2)

                # Make folders in the correct sorting root
                series_folder = sorting_root / series_name
                season_folder = series_folder / f"Season {season}"
                season_folder.mkdir(parents=True, exist_ok=True)

                # Standardize filename
                standardized_name = f"{series_name} - S{season}E{episode}{file.suffix}"
                destination = season_folder / standardized_name

                if not destination.exists():
                    shutil.move(str(file), str(destination))
                    logging.info(f"Moved {file.name} to {destination}")
                    print(f"Moved: {file.name} to {destination}")
                    sorted_files += 1
                else:
                    move_to_unsorted(file, "Duplicate file.")
                    unsorted_files += 1
                continue

            # If still no match
            logging.warning(f"Filename format not recognized: {file.name}")
            print(f"[{idx}/{total_files}] Unrecognized format: {file.name}")
            move_to_unsorted(file, "Unrecognized format.")
            unsorted_files += 1

        except Exception as e:
            logging.error(f"Unexpected error with file {file.name}: {e}")
            print(f"[{idx}/{total_files}] Error: {e}")
            move_to_unsorted(file, "Processing error.")
            unsorted_files += 1

    logging.info(f"Processing complete. Total files: {total_files}, Sorted: {sorted_files}, Unsorted: {unsorted_files}.")
    print(f"Processing complete. Total files: {total_files}, Sorted: {sorted_files}, Unsorted: {unsorted_files}.")
    return total_files, sorted_files, unsorted_files

def move_to_unsorted(file, reason):
    try:
        destination = unsorted_folder / file.name
        shutil.move(str(file), str(destination))
        logging.info(f"Moved {file.name} to Unsorted Folder: {destination} (Reason: {reason})")
        print(f"Moved {file.name} to Unsorted Folder: {destination} (Reason: {reason})")
    except Exception as e:
        logging.error(f"Failed to move {file.name} to Unsorted Folder: {e}")
        print(f"Failed to move {file.name} to Unsorted Folder: {e}")

# ... keep any additional utility functions you want, but remove/replace any conflicting or redundant logic ...