MediaSorter - How to Run
========================

IMPORTANT: When you copy MediaSorter to a new location, use one of these methods:

METHOD 1 - Windows Batch Launcher (EASIEST):
--------------------------------------------
Double-click: Start_MediaSorter.bat

This will automatically:
- Find the Brain folder
- Change to the correct directory  
- Start MediaSorter
- Work from ANY location!


METHOD 2 - Python Launcher:
---------------------------
Open command prompt in this folder and run:
python Start_MediaSorter.py


METHOD 3 - Manual (if above don't work):
----------------------------------------
1. Navigate to the "0.3 Brain Folder"
2. Double-click: run_main.bat
   OR run: python MainCodeFile.py


TROUBLESHOOTING:
===============

If MediaSorter doesn't start:
1. Make sure Python is installed
2. Make sure you have the complete folder structure:
   - 0.3 Brain Folder (with all files inside)
   - 0.1 Sorting Folder
   - 0.2 Unsorted Folder
   - Other media folders

3. Try running Start_MediaSorter.bat as Administrator

4. Check that all files were copied correctly

FOLDER STRUCTURE:
================
Your MediaSorter folder should look like this:

MediaSorter/ (or whatever you named it)
├── Start_MediaSorter.bat     ← USE THIS TO START
├── Start_MediaSorter.py      ← OR THIS
├── HOW_TO_RUN.txt           ← This file
├── 0.1 Sorting Folder/
├── 0.2 Unsorted Folder/
├── 0.3 Brain Folder/        ← Contains all the code
│   ├── MainCodeFile.py
│   ├── run_main.bat
│   └── [other files]
├── 1.0 TV Shows/
├── 2.0 Movies/
└── [other folders]

REMEMBER: Always use Start_MediaSorter.bat for the easiest experience!
