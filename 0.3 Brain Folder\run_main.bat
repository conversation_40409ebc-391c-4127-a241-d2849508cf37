@echo off
setlocal EnableDelayedExpansion

echo Starting MediaSorter...
echo Checking system requirements...

REM Check if Python is installed
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python and add it to your system PATH
    echo Visit https://www.python.org/downloads/ to download Python
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2 delims= " %%i in ('python -V 2^>^&1') do set pyver=%%i
echo Found Python version: %pyver%

REM Check if PyQt5 is installed
python -c "import PyQt5" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] PyQt5 is not installed
    echo Installing required dependencies...
    python -m pip install -r requirements.txt
    if %ERRORLEVEL% neq 0 (
        echo [ERROR] Failed to install dependencies
        echo Please try running: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

REM Check if MainCodeFile.py exists
if not exist "MainCodeFile.py" (
    echo [ERROR] MainCodeFile.py not found
    echo Please ensure you are running this script from the correct directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo All checks passed. Starting application...
echo.

REM Run the application with error handling
python MainCodeFile.py
if %ERRORLEVEL% neq 0 (
    echo.
    echo [ERROR] Application crashed or failed to start
    echo Exit Code: %ERRORLEVEL%
    echo.
    echo Please check the log file for more details: gui_log.txt
    pause
    exit /b 1
)

echo.
echo Application closed successfully
pause
