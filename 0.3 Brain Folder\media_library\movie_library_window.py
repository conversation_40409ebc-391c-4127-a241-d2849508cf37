from PyQt5.QtWidgets import (QMain<PERSON><PERSON>ow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QLabel, QTabWidget, QGridLayout, 
                             QScrollArea, QFrame, QLineEdit, QMessageBox)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QPixmap, QIcon
from pathlib import Path
import json
import os
import subprocess
import sys

# Add the parent directory to sys.path to allow absolute imports
sys.path.append(str(Path(__file__).resolve().parent.parent))
from movie_code.movie_handler import get_movies_folder

class MovieLibraryWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Movie Library")
        self.setMinimumSize(1200, 800)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Create tabs
        self.tabs = QTabWidget()
        self.tabs.setDocumentMode(True)
        main_layout.addWidget(self.tabs)
        
        # Add library tabs
        self.tabs.addTab(self.create_browse_tab(), "Browse")
        self.tabs.addTab(self.create_genres_tab(), "Genres")
        self.tabs.addTab(self.create_recent_tab(), "Recently Added")
        
        # Create search bar
        search_layout = QHBoxLayout()
        search_input = QLineEdit()
        search_input.setPlaceholderText("Search movies...")
        search_input.textChanged.connect(self.search_movies)
        search_layout.addWidget(search_input)
        main_layout.insertLayout(0, search_layout)
        
        # Store references
        self.search_input = search_input
        self.movie_grid = None
        
        # Load initial movies
        self.load_movies()
        
    def create_browse_tab(self):
        """Create the main browse tab with movie grid"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Create scroll area for movie grid
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # Create widget to hold movie grid
        grid_widget = QWidget()
        self.movie_grid = QGridLayout(grid_widget)
        self.movie_grid.setSpacing(20)
        
        scroll.setWidget(grid_widget)
        layout.addWidget(scroll)
        
        return tab
        
    def create_genres_tab(self):
        """Create the genres tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Will be populated when loading movies
        self.genres_layout = QVBoxLayout()
        layout.addLayout(self.genres_layout)
        
        return tab
        
    def create_recent_tab(self):
        """Create the recently added tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Create scroll area for recent movies
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        
        # Create widget to hold recent movies
        recent_widget = QWidget()
        self.recent_grid = QGridLayout(recent_widget)
        self.recent_grid.setSpacing(20)
        
        scroll.setWidget(recent_widget)
        layout.addWidget(scroll)
        
        return tab
        
    def load_movies(self):
        """Load all movies and populate the tabs"""
        try:
            # Clear existing layouts
            self.clear_layouts()
            
            # Get movies folder
            movies_folder = get_movies_folder()
            if not movies_folder:
                return
                
            # Track genres and recent movies
            genre_movies = {}
            recent_movies = []
            row = 0
            col = 0
            max_cols = 4
            
            # Scan all movie folders
            for movie_folder in movies_folder.iterdir():
                if movie_folder.is_dir():
                    info_folder = movie_folder / f"{movie_folder.name}_info"
                    info_file = info_folder / "media_info.json"
                    poster_file = info_folder / "posters" / "main_poster.jpg"
                    
                    if info_file.exists() and poster_file.exists():
                        # Load movie info
                        with open(info_file, 'r', encoding='utf-8') as f:
                            movie_info = json.load(f)
                            
                        # Create movie widget
                        movie_widget = self.create_movie_widget(movie_folder, info_file, poster_file)
                        
                        # Add to browse grid
                        self.movie_grid.addWidget(movie_widget, row, col)
                        
                        # Update grid position
                        col += 1
                        if col >= max_cols:
                            col = 0
                            row += 1
                            
                        # Add to genres
                        for genre in movie_info.get('genres', []):
                            genre_name = genre['name']
                            if genre_name not in genre_movies:
                                genre_movies[genre_name] = []
                            genre_movies[genre_name].append((movie_folder, movie_info))
                            
                        # Add to recent movies (store with timestamp)
                        recent_movies.append((
                            movie_folder.stat().st_mtime,
                            movie_folder,
                            movie_info
                        ))
                        
            # Populate genres tab
            for genre_name, movies in sorted(genre_movies.items()):
                self.add_genre_section(genre_name, movies)
                
            # Populate recent tab
            recent_movies.sort(reverse=True)  # Sort by newest first
            self.populate_recent_tab(recent_movies[:20])  # Show 20 most recent
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load movies: {str(e)}")
            
    def clear_layouts(self):
        """Clear all layout widgets"""
        # Clear browse grid
        if self.movie_grid:
            for i in reversed(range(self.movie_grid.count())):
                self.movie_grid.itemAt(i).widget().setParent(None)
                
        # Clear genres
        for i in reversed(range(self.genres_layout.count())):
            item = self.genres_layout.itemAt(i)
            if item.widget():
                item.widget().setParent(None)
            elif item.layout():
                self.clear_layout(item.layout())
                
        # Clear recent grid
        if hasattr(self, 'recent_grid'):
            for i in reversed(range(self.recent_grid.count())):
                self.recent_grid.itemAt(i).widget().setParent(None)
                
    def clear_layout(self, layout):
        """Recursively clear a layout"""
        while layout.count():
            item = layout.takeAt(0)
            if item.widget():
                item.widget().setParent(None)
            elif item.layout():
                self.clear_layout(item.layout())
                
    def create_movie_widget(self, movie_folder, info_file, poster_file):
        """Create a widget for a movie poster with hover effects"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        try:
            # Load movie info
            with open(info_file, 'r', encoding='utf-8') as f:
                movie_info = json.load(f)
                
            # Create poster label
            poster_label = QLabel()
            pixmap = QPixmap(str(poster_file))
            scaled_pixmap = pixmap.scaled(200, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            poster_label.setPixmap(scaled_pixmap)
            poster_label.setStyleSheet("""
                QLabel {
                    border: 2px solid transparent;
                    border-radius: 10px;
                }
                QLabel:hover {
                    border: 2px solid #0078D4;
                }
            """)
            
            # Create title label
            title_label = QLabel(movie_info.get('title', 'Unknown'))
            title_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 14px;
                    padding: 5px;
                }
            """)
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setWordWrap(True)
            
            # Add to layout
            layout.addWidget(poster_label)
            layout.addWidget(title_label)
            
            # Make widget clickable
            widget.mousePressEvent = lambda e: self.show_movie_details(movie_folder, movie_info)
            widget.setCursor(Qt.PointingHandCursor)
            widget.setStyleSheet("""
                QFrame {
                    background-color: transparent;
                }
                QFrame:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                    border-radius: 10px;
                }
            """)
            
        except Exception as e:
            # Create an error widget
            error_label = QLabel("Error loading movie")
            error_label.setStyleSheet("color: red;")
            layout.addWidget(error_label)
            
        return widget
        
    def add_genre_section(self, genre_name, movies):
        """Add a genre section to the genres tab"""
        # Add genre header
        genre_label = QLabel(genre_name)
        genre_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
            }
        """)
        self.genres_layout.addWidget(genre_label)
        
        # Create grid for this genre's movies
        grid = QGridLayout()
        grid.setSpacing(20)
        
        # Add movies to grid
        row = 0
        col = 0
        max_cols = 4
        
        for movie_folder, movie_info in movies:
            poster_file = movie_folder / f"{movie_folder.name}_info" / "posters" / "main_poster.jpg"
            if poster_file.exists():
                movie_widget = self.create_movie_widget(
                    movie_folder,
                    movie_folder / f"{movie_folder.name}_info" / "media_info.json",
                    poster_file
                )
                grid.addWidget(movie_widget, row, col)
                
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1
                    
        self.genres_layout.addLayout(grid)
        self.genres_layout.addSpacing(20)
        
    def populate_recent_tab(self, recent_movies):
        """Populate the recent tab with the most recently added movies"""
        row = 0
        col = 0
        max_cols = 4
        
        for _, movie_folder, _ in recent_movies:
            poster_file = movie_folder / f"{movie_folder.name}_info" / "posters" / "main_poster.jpg"
            if poster_file.exists():
                movie_widget = self.create_movie_widget(
                    movie_folder,
                    movie_folder / f"{movie_folder.name}_info" / "media_info.json",
                    poster_file
                )
                self.recent_grid.addWidget(movie_widget, row, col)
                
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1
                    
    def search_movies(self, query):
        """Search movies based on title, genre, or cast"""
        try:
            # Clear existing grid
            self.clear_layouts()
            
            if not query:  # If search is empty, show all movies
                self.load_movies()
                return
                
            # Get movies folder
            movies_folder = get_movies_folder()
            if not movies_folder:
                return
                
            query = query.lower()
            row = 0
            col = 0
            max_cols = 4
            
            for movie_folder in movies_folder.iterdir():
                if movie_folder.is_dir():
                    info_folder = movie_folder / f"{movie_folder.name}_info"
                    info_file = info_folder / "media_info.json"
                    poster_file = info_folder / "posters" / "main_poster.jpg"
                    
                    if info_file.exists() and poster_file.exists():
                        with open(info_file, 'r', encoding='utf-8') as f:
                            movie_info = json.load(f)
                            
                        # Search in title
                        title = movie_info.get('title', '').lower()
                        # Search in genres
                        genres = [g['name'].lower() for g in movie_info.get('genres', [])]
                        # Search in cast
                        cast = [c['name'].lower() for c in movie_info.get('credits', {}).get('cast', [])]
                        
                        if (query in title or 
                            any(query in genre for genre in genres) or 
                            any(query in actor for actor in cast)):
                            
                            movie_widget = self.create_movie_widget(movie_folder, info_file, poster_file)
                            self.movie_grid.addWidget(movie_widget, row, col)
                            
                            col += 1
                            if col >= max_cols:
                                col = 0
                                row += 1
                                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to search movies: {str(e)}")
            
    def show_movie_details(self, movie_folder, movie_info):
        """Show detailed movie information in a dialog"""
        from .movie_details_dialog import MovieDetailsDialog
        dialog = MovieDetailsDialog(movie_folder, movie_info, self)
        dialog.exec_() 