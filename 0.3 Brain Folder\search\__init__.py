from .search_utils import (
    detect_media_type,
    format_file_size,
    get_file_metadata,
    parse_date_range,
    create_search_index
)
from .search_results import SearchResults
from .search_config import SearchConfig

class SearchHandler:
    def __init__(self, root_folder=None):
        """
        Initialize the search handler with optional root folder.
        If not provided, uses the parent directory of the search module.
        """
        if root_folder is None:
            root_folder = str(__file__).rsplit('search', 1)[0]
            
        self.root_folder = root_folder
        self.config = SearchConfig()
        self.results = SearchResults()
        self._index = None
        
    def scan_folders(self, custom_root=None):
        """Scan folders and update search index"""
        scan_path = custom_root or self.root_folder
        self._index = create_search_index(scan_path)
        
    def search(self, query="", **filters):
        """
        Perform a search with the given query and filters.
        Filters can include: media_type, min_size, max_size, start_date, end_date
        """
        if self._index is None:
            self.scan_folders()
            
        # Get matching files from index
        if query and self._index:
            matching_files = set()
            for word in query.lower().split():
                if word in self._index:
                    if not matching_files:
                        matching_files.update(self._index[word])
                    else:
                        matching_files.intersection_update(self._index[word])
        else:
            # If no query, include all files
            matching_files = {
                path
                for paths in (self._index or {}).values()
                for path in paths
            }
            
        # Update results with matching files
        self.results.update(list(matching_files), query)
        
        # Apply filters if any
        if filters:
            return self.results.filter(**filters)
            
        return self.results.results
        
    def get_file_types(self):
        """Get list of available file types"""
        return self.results.get_unique_values('media_type')
        
    def get_statistics(self):
        """Get search statistics"""
        return self.results.get_statistics()
        
    def sort_results(self, key='name', reverse=False):
        """Sort current results"""
        self.results.sort(key=key, reverse=reverse) 