from typing import List, Dict, Optional, Callable
from datetime import datetime
from pathlib import Path
from .search_utils import get_file_metadata

class SearchResults:
    def __init__(self):
        self.results: List[Dict] = []
        self._cache: Dict[str, List[Dict]] = {}
        self.last_query: Optional[str] = None
        
    def update(self, files: List[str], query: str = ""):
        """Update search results with new files"""
        self.results = []
        self.last_query = query
        
        # Check cache first
        cache_key = f"{query}:{','.join(sorted(files))}"
        if cache_key in self._cache:
            self.results = self._cache[cache_key]
            return
            
        # Process new files
        for file_path in files:
            try:
                metadata = get_file_metadata(file_path)
                if self._matches_query(metadata, query):
                    self.results.append(metadata)
            except (OSError, PermissionError):
                continue
                
        # Cache results
        self._cache[cache_key] = self.results
        
        # Limit cache size
        if len(self._cache) > 100:
            # Remove oldest entries
            oldest = list(self._cache.keys())[:-50]
            for key in oldest:
                del self._cache[key]
    
    def _matches_query(self, metadata: Dict, query: str) -> bool:
        """Check if file metadata matches search query"""
        if not query:
            return True
            
        query = query.lower()
        
        # Check filename
        if query in metadata['name'].lower():
            return True
            
        # Check media type
        if query in metadata['media_type'].lower():
            return True
            
        # Check extension
        if query == metadata['extension'].lower().lstrip('.'):
            return True
            
        return False
    
    def sort(self, key: str = 'name', reverse: bool = False):
        """Sort results by the specified key"""
        if not self.results:
            return
            
        sort_functions: Dict[str, Callable] = {
            'name': lambda x: x['name'].lower(),
            'size': lambda x: x['size'],
            'type': lambda x: x['media_type'],
            'date': lambda x: x['modified'],
            'extension': lambda x: x['extension'].lower()
        }
        
        if key in sort_functions:
            self.results.sort(key=sort_functions[key], reverse=reverse)
    
    def filter(self, 
               media_type: Optional[str] = None,
               min_size: Optional[int] = None,
               max_size: Optional[int] = None,
               start_date: Optional[datetime] = None,
               end_date: Optional[datetime] = None,
               extensions: Optional[List[str]] = None) -> List[Dict]:
        """Filter results based on criteria"""
        filtered = []
        
        for item in self.results:
            # Media type filter
            if media_type and item['media_type'] != media_type:
                continue
                
            # Size filter
            if min_size is not None and item['size'] < min_size:
                continue
            if max_size is not None and item['size'] > max_size:
                continue
                
            # Date filter
            if start_date and item['modified'] < start_date:
                continue
            if end_date and item['modified'] > end_date:
                continue
                
            # Extension filter
            if extensions:
                if item['extension'].lower().lstrip('.') not in extensions:
                    continue
                    
            filtered.append(item)
            
        return filtered
    
    def get_unique_values(self, field: str) -> List:
        """Get list of unique values for a field in the results"""
        if not self.results:
            return []
            
        values = set()
        for item in self.results:
            if field in item:
                values.add(item[field])
                
        return sorted(list(values))
    
    def get_statistics(self) -> Dict:
        """Get statistics about the search results"""
        if not self.results:
            return {
                'total_files': 0,
                'total_size': 0,
                'avg_size': 0,
                'media_types': {},
                'extensions': {}
            }
            
        stats = {
            'total_files': len(self.results),
            'total_size': sum(item['size'] for item in self.results),
            'media_types': {},
            'extensions': {}
        }
        
        # Calculate media type distribution
        for item in self.results:
            media_type = item['media_type']
            stats['media_types'][media_type] = stats['media_types'].get(media_type, 0) + 1
            
            ext = item['extension'].lower().lstrip('.')
            stats['extensions'][ext] = stats['extensions'].get(ext, 0) + 1
            
        stats['avg_size'] = stats['total_size'] / stats['total_files']
        
        return stats 