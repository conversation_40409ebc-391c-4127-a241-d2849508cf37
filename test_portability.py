#!/usr/bin/env python3
"""
Test script to verify MediaSorter portability
This script tests that the path finding functions work correctly from any location.
"""

import sys
import os
from pathlib import Path

# Add the Brain folder to the path so we can import the modules
brain_folder = Path(__file__).parent / "0.3 Brain Folder"
sys.path.insert(0, str(brain_folder))

def test_main_code_import():
    """Test that we can import the main code and find the root"""
    try:
        # Import the find_mediasorter_root function from MainCodeFile
        import MainCodeFile
        root = MainCodeFile.find_mediasorter_root()
        print(f"✅ MainCodeFile root detection: {root}")
        return True
    except Exception as e:
        print(f"❌ MainCodeFile import failed: {e}")
        return False

def test_movie_handler_import():
    """Test that movie handler can find the root"""
    try:
        sys.path.insert(0, str(brain_folder / "movie_code"))
        import movie_handler
        root = movie_handler.find_mediasorter_root()
        print(f"✅ Movie handler root detection: {root}")
        return True
    except Exception as e:
        print(f"❌ Movie handler import failed: {e}")
        return False

def test_tv_show_import():
    """Test that TV show module can find the root"""
    try:
        sys.path.insert(0, str(brain_folder / "tv_show_code"))
        import tv_show
        root = tv_show.find_mediasorter_root()
        print(f"✅ TV show root detection: {root}")
        return True
    except Exception as e:
        print(f"❌ TV show import failed: {e}")
        return False

def test_settings_import():
    """Test that settings manager can find the root"""
    try:
        sys.path.insert(0, str(brain_folder / "settings"))
        import settings_manager
        root = settings_manager.find_mediasorter_root()
        print(f"✅ Settings manager root detection: {root}")
        return True
    except Exception as e:
        print(f"❌ Settings manager import failed: {e}")
        return False

def test_folder_structure():
    """Test that the expected folder structure exists"""
    script_dir = Path(__file__).parent
    expected_folders = [
        "0.1 Sorting Folder",
        "0.2 Unsorted Folder", 
        "0.3 Brain Folder",
        "1.0 TV Shows",
        "2.0 Movies"
    ]
    
    print("\n📁 Checking folder structure:")
    all_exist = True
    for folder in expected_folders:
        folder_path = script_dir / folder
        if folder_path.exists():
            print(f"✅ {folder}")
        else:
            print(f"❌ {folder} (missing)")
            all_exist = False
    
    return all_exist

def main():
    print("MediaSorter Portability Test")
    print("=" * 30)
    
    # Check if we're in the right directory
    script_dir = Path(__file__).parent
    brain_folder_path = script_dir / "0.3 Brain Folder"
    
    if not brain_folder_path.exists():
        print("❌ Error: 0.3 Brain Folder not found!")
        print(f"   Current directory: {script_dir}")
        print("   Please run this test from the MediaSorter root directory.")
        return False
    
    print(f"📍 Testing from: {script_dir}")
    print(f"🧠 Brain folder: {brain_folder_path}")
    print()
    
    # Run all tests
    tests = [
        test_folder_structure,
        test_main_code_import,
        test_movie_handler_import,
        test_tv_show_import,
        test_settings_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MediaSorter is fully portable.")
        return True
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
