# MediaSorter - Portable Version

MediaSorter is now fully portable! You can move the entire MediaSorter folder to any location on your computer and it will work without any configuration changes.

## How to Run MediaSorter

### Option 1: Windows Batch Launcher (Recommended for Windows)
Double-click `MediaSorter.bat` in the main folder. This launcher will:
- Automatically find the MediaSorter installation
- Check for Python and required dependencies
- Start the application

### Option 2: Cross-Platform Python Launcher
Run `run_mediasorter.py` from any location:
```bash
python run_mediasorter.py
```

### Option 3: Direct Launch (Advanced Users)
Navigate to the `0.3 Brain Folder` and run:
```bash
python MainCodeFile.py
```

## What's Changed

### ✅ Portability Improvements
- **No more hardcoded paths**: The application now automatically detects its location
- **Folder name independent**: You can rename the main MediaSorter folder to anything you want
- **Location independent**: Move the folder anywhere on your system
- **Cross-platform launchers**: Works on Windows, Mac, and Linux

### ✅ Fixed Issues
- Application no longer crashes when moved to a different directory
- Settings are preserved when relocating the folder
- All file paths are now relative to the application location
- <PERSON>ch file automatically sets the correct working directory

## Folder Structure

The application identifies itself by looking for the `0.3 Brain Folder` directory. As long as this folder structure is maintained, MediaSorter will work from any location:

```
MediaSorter/                    (can be renamed to anything)
├── MediaSorter.bat            (Windows launcher)
├── run_mediasorter.py         (Cross-platform launcher)
├── requirements.txt           (Python dependencies)
├── 0.1 Sorting Folder/        (Temporary sorting location)
├── 0.2 Unsorted Folder/       (Files that couldn't be sorted)
├── 0.3 Brain Folder/          (Main application code)
│   ├── MainCodeFile.py        (Main application)
│   ├── run_main.bat           (Direct launcher)
│   ├── config.json            (API configuration)
│   ├── settings.json          (Application settings)
│   └── [other code folders]
├── 1.0 TV Shows/              (Default TV show destination)
├── 2.0 Movies/                (Default movie destination)
└── [other media folders]
```

## Requirements

- Python 3.6 or higher
- PyQt5 (automatically installed if missing)
- Internet connection (for movie/TV show metadata)

## First Time Setup

1. Extract or move the MediaSorter folder to your desired location
2. Double-click `MediaSorter.bat` (Windows) or run `python run_mediasorter.py`
3. The application will automatically install required dependencies
4. Configure your TMDB API key in the settings (optional but recommended)

## Troubleshooting

### "Python not found" error
- Install Python from https://python.org/downloads/
- Make sure to check "Add Python to PATH" during installation

### Application won't start
- Make sure you're running the launcher from the main MediaSorter folder
- Check that the `0.3 Brain Folder` exists and contains `MainCodeFile.py`
- Try running `python run_mediasorter.py` for more detailed error messages

### Dependencies not installing
- Run manually: `pip install -r requirements.txt`
- Make sure you have an internet connection

## Moving MediaSorter

To move MediaSorter to a new location:
1. Copy the entire MediaSorter folder to the new location
2. Run the launcher from the new location
3. That's it! No configuration changes needed.

The application will automatically:
- Detect the new location
- Update all internal paths
- Preserve your settings and configurations
- Continue working exactly as before

## Support

If you encounter any issues with the portable version, please check:
1. The folder structure is intact
2. Python is properly installed
3. You have write permissions in the MediaSorter folder
4. The `0.3 Brain Folder` contains all the necessary files
