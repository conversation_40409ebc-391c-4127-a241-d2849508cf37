#!/usr/bin/env python3
"""
MediaSorter Universal Launcher
This launcher works from any location and automatically finds and runs MediaSorter.
"""

import os
import sys
import subprocess
from pathlib import Path

def find_brain_folder():
    """Find the Brain folder starting from the script location"""
    script_dir = Path(__file__).resolve().parent
    
    print(f"🔍 Looking for MediaSorter from: {script_dir}")
    
    # Check if Brain folder exists in current directory
    brain_folder = script_dir / "0.3 Brain Folder"
    if brain_folder.exists() and brain_folder.is_dir():
        main_file = brain_folder / "MainCodeFile.py"
        if main_file.exists():
            print(f"✅ Found Brain folder: {brain_folder}")
            return brain_folder
    
    print(f"❌ Brain folder not found at: {brain_folder}")
    return None

def check_python():
    """Check if Python is working"""
    try:
        version = sys.version.split()[0]
        print(f"✅ Python version: {version}")
        return True
    except:
        print("❌ Python check failed")
        return False

def main():
    print("MediaSorter Universal Launcher")
    print("=" * 30)
    print()
    
    # Check Python
    if not check_python():
        input("Press Enter to exit...")
        return False
    
    # Find the Brain folder
    brain_folder = find_brain_folder()
    
    if not brain_folder:
        print()
        print("❌ ERROR: MediaSorter Brain folder not found!")
        print()
        print("Please ensure this launcher is in the same directory as '0.3 Brain Folder'")
        print(f"Current directory: {Path(__file__).resolve().parent}")
        print()
        print("Expected structure:")
        print("  [Your Folder]/")
        print("  ├── Start_MediaSorter.py  (this file)")
        print("  ├── 0.3 Brain Folder/")
        print("  │   ├── MainCodeFile.py")
        print("  │   └── [other files]")
        print("  └── [other folders]")
        print()
        input("Press Enter to exit...")
        return False
    
    # Find the main Python file
    main_file = brain_folder / "MainCodeFile.py"
    if not main_file.exists():
        print(f"❌ ERROR: MainCodeFile.py not found in {brain_folder}")
        input("Press Enter to exit...")
        return False
    
    print(f"🚀 Starting MediaSorter from: {brain_folder}")
    print()
    
    # Change to the Brain folder directory
    original_dir = os.getcwd()
    try:
        os.chdir(brain_folder)
        print(f"📁 Changed to directory: {os.getcwd()}")
        
        # Run the main Python file
        print("🎬 Launching MediaSorter...")
        print()
        
        result = subprocess.run([sys.executable, "MainCodeFile.py"])
        
        if result.returncode == 0:
            print()
            print("✅ MediaSorter closed successfully")
        else:
            print()
            print(f"⚠️  MediaSorter exited with code: {result.returncode}")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ ERROR: MediaSorter failed to start: {e}")
        input("Press Enter to exit...")
        return False
    except KeyboardInterrupt:
        print()
        print("⏹️  MediaSorter was interrupted by user")
    except Exception as e:
        print(f"❌ ERROR: Unexpected error: {e}")
        input("Press Enter to exit...")
        return False
    finally:
        # Change back to original directory
        os.chdir(original_dir)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"❌ FATAL ERROR: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
