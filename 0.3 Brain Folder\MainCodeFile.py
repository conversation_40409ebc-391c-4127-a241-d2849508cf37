# MainCodeFile.py v4.1

import sys
import shutil
import logging
import re  # Added missing import for regex
from pathlib import Path
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QLabel,
    QTextEdit, QTabWidget, QListWidget, QAbstractItemView, QProgressBar, QFileDialog,
    QMessageBox, QGridLayout, QHBoxLayout, QFrame, QLineEdit, QGroupBox, QComboBox, QListWidgetItem,
    QProgressDialog, QInputDialog, QScrollArea, QDialog, QAction, QCheckBox, QSpinBox, QSizePolicy
)
from PyQt5.QtCore import Qt, QSize, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QDragEnterEvent, QDropEvent, QColor, QIcon, QPixmap, QFont, QPalette, QDrag, QCursor
import os
import subprocess
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler
import requests
import json
from PIL import Image
from io import BytesIO
import threading
import queue
import tmdbv3api
from theme.theme_manager import ThemeManager

# Add access to the sorting logic files
sys.path.append(str(Path(__file__).resolve().parent / "tv_show_code"))
sys.path.append(str(Path(__file__).resolve().parent / "movie_code"))
sys.path.append(str(Path(__file__).resolve().parent / "media_library"))  # Add media library to path
import tv_show_code as file_sorter  # Import with alias to maintain compatibility
from settings.settings_manager import get_settings_widget, load_settings, save_settings, FolderSettingWidget
from status.health_checker import HealthStatus  # Add import for HealthStatus
from theme.theme_manager import ThemeManager, Theme
from tv_show_code.tv_show import process_files  # Import process_files directly from tv_show
from movie_code.movie_handler import process_gui_movies, get_movies_folder, movie_logger
from movie_code.movie_tab import MovieTab  # Import our new MovieTab class
from media_library.media_library_tab import MediaLibraryTab
from media_library.plex_style_library import PlexStyleMovieLibrary
from tv_show_code.tv_show_tab import TVShowTab

class TMDBHelper:
    """Helper class for TMDB API interactions"""
    BASE_URL = "https://api.themoviedb.org/3"
    IMAGE_BASE_URL = "https://image.tmdb.org/t/p/"

    def __init__(self):
        self.api_key = None

    def initialize(self):
        """Initialize TMDB API key"""
        try:
            config_file = Path(__file__).parent / "config.json"
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    self.api_key = config.get('tmdb_api_key')

            if not self.api_key:
                return False
            return True
        except Exception:
            return False

    def search_media(self, title, media_type='multi'):
        """Search for a movie, TV show, or person"""
        if not self.api_key:
            raise ValueError("TMDB API key not configured")

        url = f"{self.BASE_URL}/search/{media_type}"
        params = {
            'api_key': self.api_key,
            'query': title,
            'language': 'en-US'
        }

        response = requests.get(url, params=params)
        if response.ok:
            # Also search with original language for anime
            params['language'] = 'ja-JP'
            jp_response = requests.get(url, params=params)

            results = response.json()
            if jp_response.ok:
                # Merge Japanese results for better anime detection
                jp_results = jp_response.json()
                results['results'].extend(jp_results.get('results', []))

            return results
        return None

    def get_details(self, media_id, media_type):
        """Get detailed information about a specific movie or TV show"""
        if not self.api_key:
            raise ValueError("TMDB API key not configured")

        url = f"{self.BASE_URL}/{media_type}/{media_id}"
        params = {
            'api_key': self.api_key,
            'language': 'en-US',
            'append_to_response': 'credits,images,videos,alternative_titles,keywords,translations'
        }

        # Get English details
        response = requests.get(url, params=params)
        if not response.ok:
            return None

        details = response.json()

        # Get Japanese details for anime
        params['language'] = 'ja-JP'
        jp_response = requests.get(url, params=params)
        if jp_response.ok:
            jp_details = jp_response.json()
            # Merge relevant Japanese information
            details['original_title_jp'] = jp_details.get('original_title') or jp_details.get('original_name')
            details['overview_jp'] = jp_details.get('overview')

        return details

    def get_image_url(self, path, size='original'):
        """Get the full URL for an image"""
        return f"{self.IMAGE_BASE_URL}{size}/{path}" if path else None

    def is_likely_anime(self, details):
        """Determine if the media is likely an anime"""
        # Check various indicators that suggest this is anime
        keywords = [keyword['name'].lower() for keyword in details.get('keywords', {}).get('keywords', [])]
        production_countries = [c['iso_3166_1'] for c in details.get('production_countries', [])]

        return (
            'anime' in keywords or
            'japanese animation' in keywords or
            'JP' in production_countries or
            (details.get('original_language') == 'ja' and
             any(g['name'] in ['Animation', 'Anime'] for g in details.get('genres', [])))
        )

# UTILITY FUNCTION:
# Finds the root directory of the MediaSorter application
def find_mediasorter_root():
    """
    Find the MediaSorter root directory by going up from the Brain folder.
    This works regardless of where the main folder is located.
    """
    # Start from this file's location (in Brain folder)
    current = Path(__file__).resolve().parent

    # If we're in the Brain folder, go up one level to get the root
    if current.name == "0.3 Brain Folder":
        return current.parent

    # Fallback: assume we're one level down from root
    return current.parent

# LOGGING SETUP:
# Configures logging to write to Brain folder
main_folder = find_mediasorter_root()
log_dir = main_folder / '0.3 Brain Folder' / 'logs'
log_dir.mkdir(parents=True, exist_ok=True)
log_file = log_dir / 'gui_log_1.txt'
handler = RotatingFileHandler(
    str(log_file), maxBytes=1_000_000, backupCount=3
)
logging.basicConfig(
    handlers=[handler],
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Test log entry
logging.info("MediaSorter application starting - Log test")

# CUSTOM WIDGET CLASS:
# Custom QListWidget with drag-and-drop functionality for files
class FileListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDragDropMode(QAbstractItemView.DropOnly)
        self.setAlternatingRowColors(True)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.main_window = None
        self.file_paths = []
        self.is_dragging = False

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            self.is_dragging = True
            event.accept()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        self.is_dragging = False
        super().dragLeaveEvent(event)

    def dragMoveEvent(self, event):
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        self.is_dragging = False
        if not event.mimeData().hasUrls():
            event.ignore()
            return

        event.accept()
        try:
            file_paths = []
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                path = Path(file_path)
                if path.is_file():
                    if str(path) not in self.file_paths:  # Prevent duplicates
                        file_paths.append(str(path))
                        self.addItem(path.name)
                        if self.main_window:
                            self.main_window.log_action(f"Added file: {path.name}")

            # Add all files at once
            if file_paths:
                self.file_paths.extend(file_paths)
                if self.main_window:
                    self.main_window.log_action(f"Successfully added {len(file_paths)} files")

        except Exception as e:
            logging.error(f"Error in drop event: {e}")
            if self.main_window:
                self.main_window.log_action(f"Error adding files: {e}")

    def add_files(self, file_paths):
        """Add files to the list widget with error handling"""
        try:
            added_count = 0
            for file_path in file_paths:
                path = Path(file_path)
                if path.is_file():
                    if str(path) not in self.file_paths:  # Prevent duplicates
                        self.file_paths.append(str(path))
                        self.addItem(path.name)
                        added_count += 1

            if added_count > 0 and self.main_window:
                self.main_window.log_action(f"Successfully added {added_count} files")

        except Exception as e:
            logging.error(f"Error adding files: {e}")
            if self.main_window:
                self.main_window.log_action(f"Error adding files: {e}")

    def clear(self):
        """Override clear to also clear file paths"""
        super().clear()
        self.file_paths = []

    def set_main_window(self, main_window):
        self.main_window = main_window

# CUSTOM WIDGET CLASS:
# Custom QListWidget with drag-and-drop functionality for movie files
class MovieListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDragDropMode(QAbstractItemView.DropOnly)
        self.setAlternatingRowColors(True)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.main_window = None
        self.file_paths = []
        self.is_dragging = False
        self.movie_extensions = {'.mp4', '.mkv', '.avi', '.mov', '.wmv'}

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            self.is_dragging = True
            event.accept()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        self.is_dragging = False
        super().dragLeaveEvent(event)

    def dragMoveEvent(self, event):
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        self.is_dragging = False
        if not event.mimeData().hasUrls():
            event.ignore()
            return

        event.accept()
        try:
            file_paths = []
            skipped_files = []

            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                path = Path(file_path)
                if path.is_file():
                    if path.suffix.lower() in self.movie_extensions:
                        if str(path) not in self.file_paths:  # Prevent duplicates
                            file_paths.append(str(path))
                            self.addItem(path.name)
                            if self.main_window:
                                self.main_window.log_action(f"Added movie file: {path.name}")
                    else:
                        skipped_files.append(path.name)

            # Add all valid files at once
            if file_paths:
                self.file_paths.extend(file_paths)
                if self.main_window:
                    self.main_window.log_action(f"Successfully added {len(file_paths)} movie files")

            # Report skipped files
            if skipped_files and self.main_window:
                self.main_window.log_action(f"Skipped {len(skipped_files)} non-movie files")

        except Exception as e:
            if self.main_window:
                self.main_window.log_action(f"Error adding movie files: {e}")

    def add_files(self, file_paths):
        """Add movie files to the list widget with improved error handling"""
        try:
            added_count = 0
            skipped_count = 0

            for file_path in file_paths:
                path = Path(file_path)
                if path.is_file():
                    if path.suffix.lower() in self.movie_extensions:
                        if str(path) not in self.file_paths:  # Prevent duplicates
                            self.file_paths.append(str(path))
                            self.addItem(path.name)
                            added_count += 1
                    else:
                        skipped_count += 1

            if self.main_window:
                if added_count > 0:
                    self.main_window.log_action(f"Successfully added {added_count} movie files")
                if skipped_count > 0:
                    self.main_window.log_action(f"Skipped {skipped_count} non-movie files")

        except Exception as e:
            if self.main_window:
                self.main_window.log_action(f"Error adding movie files: {e}")

    def clear(self):
        """Override clear to also clear file paths"""
        super().clear()
        self.file_paths = []

    def set_main_window(self, main_window):
        self.main_window = main_window

# MAIN WINDOW CLASS:
# The primary application window containing all UI elements and functionality
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("MediaSorter")
        self.setMinimumSize(1024, 768)

        # Initialize settings and theme manager
        self.settings = self.load_settings()
        self.theme_manager = ThemeManager()

        # Initialize health indicators
        self.health_indicators = {}
        self.health_messages = {}

        # Initialize file lists
        self.movie_files = []

        # Initialize folder paths
        self.main_folder = find_mediasorter_root()
        self.sorting_folder = self.main_folder / "0.1 Sorting Folder"
        if not self.sorting_folder.exists():
            self.sorting_folder.mkdir(parents=True, exist_ok=True)
            self.log_action(f"Created missing directory: {self.sorting_folder}")

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Initialize UI and apply theme
        self.initUI()
        self.apply_theme()

        # Show window
        self.show()
        self.log_action("Application started")

    def load_settings(self):
        """Load settings from file"""
        try:
            settings_file = Path(__file__).parent / "settings.json"
            if settings_file.exists():
                with open(settings_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading settings: {e}")
        return {}

    def apply_theme(self):
        """Apply the current theme to all UI components."""
        palette = self.theme_manager.get_palette()
        is_dark = self.theme_manager.is_dark_mode()

        # Define colors based on theme
        if is_dark:
            bg_color = "#141414"
            text_color = "#ffffff"
            secondary_bg = "#2d2d2d"
            hover_bg = "#3d3d3d"
            border_color = "#404040"
            button_bg = "rgba(255, 255, 255, 0.1)"
            button_hover = "rgba(255, 255, 255, 0.2)"
            input_bg = "rgba(255, 255, 255, 0.1)"
            list_bg = "#202020"
            list_hover = "#303030"
            list_selected = "#404040"
        else:
            bg_color = "#ffffff"
            text_color = "#000000"
            secondary_bg = "#f8f9fa"
            hover_bg = "#e9ecef"
            border_color = "#dee2e6"
            button_bg = "#e9ecef"
            button_hover = "#dee2e6"
            input_bg = "#ffffff"
            list_bg = "#ffffff"
            list_hover = "#f8f9fa"
            list_selected = "#e9ecef"

        # Create base stylesheet
        base_style = f"""
            QMainWindow, QWidget {{
                background: {bg_color};
                color: {text_color};
            }}

            QTabWidget::pane {{
                border: none;
                background: {bg_color};
            }}

            QTabBar::tab {{
                background: {secondary_bg};
                color: {text_color};
                padding: 8px 20px;
                border: none;
                margin-right: 2px;
            }}

            QTabBar::tab:selected {{
                background: {hover_bg};
                border-bottom: 2px solid #e50914;
            }}

            QTabBar::tab:hover:!selected {{
                background: {hover_bg};
            }}

            QPushButton {{
                background: {button_bg};
                color: {text_color};
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }}

            QPushButton:hover {{
                background: {button_hover};
            }}

            QLineEdit {{
                background: {input_bg};
                color: {text_color};
                border: 1px solid {border_color};
                border-radius: 4px;
                padding: 8px;
            }}

            QLineEdit:focus {{
                border-color: #007bff;
            }}

            QListWidget {{
                background: {list_bg};
                color: {text_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}

            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {border_color};
            }}

            QListWidget::item:hover {{
                background: {list_hover};
            }}

            QListWidget::item:selected {{
                background: {list_selected};
                color: {text_color};
            }}

            QComboBox {{
                background: {input_bg};
                color: {text_color};
                border: 1px solid {border_color};
                border-radius: 4px;
                padding: 6px;
            }}

            QComboBox:hover {{
                border-color: #007bff;
            }}

            QComboBox QAbstractItemView {{
                background: {list_bg};
                color: {text_color};
                selection-background-color: {list_selected};
                selection-color: {text_color};
            }}

            QProgressBar {{
                border: 1px solid {border_color};
                border-radius: 4px;
                text-align: center;
            }}

            QProgressBar::chunk {{
                background-color: #007bff;
                border-radius: 3px;
            }}

            QScrollBar:vertical {{
                border: none;
                background: {secondary_bg};
                width: 10px;
                margin: 0px;
            }}

            QScrollBar::handle:vertical {{
                background: {hover_bg};
                min-height: 20px;
                border-radius: 5px;
            }}

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                border: none;
                background: none;
            }}

            QLabel {{
                color: {text_color};
            }}

            QTextEdit {{
                background: {list_bg};
                color: {text_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
        """

        # Apply theme to main window
        self.setStyleSheet(base_style)

        # Update theme button text
        if hasattr(self, 'theme_button'):
            self.theme_button.setText("☀️ Light Mode" if is_dark else "🌙 Dark Mode")

        # Apply theme to all tabs
        for i in range(self.tabs.count()):
            tab = self.tabs.widget(i)
            if tab:
                tab.setStyleSheet(base_style)

        # Apply theme to specific components
        if hasattr(self, 'logs_display'):
            self.logs_display.setStyleSheet(base_style)

        if hasattr(self, 'search_results'):
            self.search_results.setStyleSheet(base_style)

        if hasattr(self, 'movie_list'):
            self.movie_list.setStyleSheet(base_style)

        if hasattr(self, 'file_list'):
            self.file_list.setStyleSheet(base_style)

        # Refresh the UI
        self.update()

    def toggle_theme(self):
        """Toggle between light and dark themes."""
        self.theme_manager.toggle_theme()
        self.apply_theme()

    # UI INITIALIZATION:
    # Creates the main UI components and tab structure
    def initUI(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # Add header with buttons
        header = QFrame()
        header_layout = QHBoxLayout()
        header.setLayout(header_layout)

        # Theme settings button
        theme_settings_button = QPushButton("⚙️ Theme Settings")
        theme_settings_button.setFixedWidth(120)
        theme_settings_button.clicked.connect(self.open_theme_settings)
        header_layout.addWidget(theme_settings_button)

        # API config button
        api_button = QPushButton("🔑 Configure API")
        api_button.setFixedWidth(120)
        api_button.clicked.connect(self.configure_tmdb_api)
        header_layout.addWidget(api_button)

        # Add stretch to push buttons to the right
        header_layout.addStretch()

        main_layout.addWidget(header)

        # Tabs
        self.tabs = QTabWidget()
        self.tabs.setDocumentMode(True)  # Modern tab style

        # Add all tabs
        self.tabs.addTab(TVShowTab(self), "TV Shows")  # Use the new TVShowTab class
        self.tabs.addTab(MovieTab(self), "Movies")  # Use the new MovieTab class
        self.tabs.addTab(self.create_search_tab(), "Search")
        self.tabs.addTab(self.create_status_tab(), "Status")
        self.tabs.addTab(self.create_settings_tab(), "Settings")
        self.tabs.addTab(self.create_logs_tab(), "Logs")

        main_layout.addWidget(self.tabs)

        # Create necessary folders if they don't exist
        self.create_sorting_folders()

    def create_sorting_folders(self):
        """Create all necessary sorting folders if they don't exist"""
        try:
            # Define the base folders
            base_folders = [
                "0.1 Sorting Folder",
                "0.2 Unsorted Folder",
                "0.3 Brain Folder",
                "1.0 TV Shows",
                "2.0 Movies"
            ]

            # Create each folder
            for folder in base_folders:
                folder_path = self.main_folder / folder
                if not folder_path.exists():
                    folder_path.mkdir(parents=True, exist_ok=True)
                    self.log_action(f"Created missing directory: {folder_path}")

        except Exception as e:
            self.log_action(f"Error creating sorting folders: {e}")
            QMessageBox.warning(self, "Warning", f"Failed to create some sorting folders: {e}")



    def create_file_sorting_tab(self):
        """Creates a dedicated tab for TV show sorting"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Create header with title and description
        header = QFrame()
        header_layout = QVBoxLayout()
        header.setLayout(header_layout)

        title = QLabel("TV Show Sorting")
        title.setStyleSheet("font-size: 18px; font-weight: bold;")
        header_layout.addWidget(title)

        description = QLabel("Sort your TV show files into organized folders automatically.")
        description.setWordWrap(True)
        header_layout.addWidget(description)

        layout.addWidget(header)

        # Create a horizontal layout for destinations and file drop area
        content_layout = QHBoxLayout()

        # Create folder toggles section with scroll area
        folders_group = QGroupBox("TV Show Sort Destinations")
        folders_group.setMinimumWidth(300)
        folders_group.setMaximumWidth(400)
        folders_layout = QVBoxLayout()
        folders_group.setLayout(folders_layout)

        # Create scroll area for destinations
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create a widget to hold all folder widgets
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout()
        scroll_content.setLayout(scroll_layout)

        # Add folder widgets
        self.folder_widgets = []
        settings = load_settings()
        for folder_data in settings.get("custom_folders", []):
            if folder_data.get("sort_type") == "TV Shows":
                widget = TVShowDestinationWidget(folder_data, settings)
                self.folder_widgets.append(widget)
                scroll_layout.addWidget(widget)

        # Add button to create new folder
        add_folder_btn = QPushButton("+ Add New TV Show Sort Destination")
        add_folder_btn.clicked.connect(lambda: self.add_new_tv_destination(scroll_layout))
        scroll_layout.addWidget(add_folder_btn)

        # Add stretch to push everything to the top
        scroll_layout.addStretch()

        # Set the scroll content
        scroll_area.setWidget(scroll_content)
        folders_layout.addWidget(scroll_area)

        # Add the folders group to the content layout
        content_layout.addWidget(folders_group)

        # Create right side content for file drop
        right_content = QWidget()
        right_layout = QVBoxLayout()
        right_content.setLayout(right_layout)

        # Instructions
        drop_label = QLabel("Drag and drop TV show files here or use the 'Add Files' button")
        drop_label.setAlignment(Qt.AlignCenter)
        right_layout.addWidget(drop_label)

        # File list
        self.file_list = FileListWidget()
        self.file_list.set_main_window(self)
        right_layout.addWidget(self.file_list)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        right_layout.addWidget(self.progress_bar)

        # Buttons
        buttons_layout = QHBoxLayout()

        add_files_button = QPushButton("Add Files")
        add_files_button.clicked.connect(self.add_files)
        buttons_layout.addWidget(add_files_button)

        sort_button = QPushButton("Sort Files")
        sort_button.clicked.connect(self.sort_files)
        buttons_layout.addWidget(sort_button)

        right_layout.addLayout(buttons_layout)

        # Add the right content to the main content layout
        content_layout.addWidget(right_content, stretch=1)

        # Add the content layout to the main layout
        layout.addLayout(content_layout)

        return tab

    def create_movies_tab(self):
        """Creates a dedicated tab for movie sorting"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Create content layout
        content_layout = QHBoxLayout()

        # Left content (file list)
        left_content = QWidget()
        left_layout = QVBoxLayout()
        left_content.setLayout(left_layout)

        # File list
        self.movie_list = FileListWidget()
        left_layout.addWidget(self.movie_list)

        # Add the left content to the main content layout
        content_layout.addWidget(left_content, stretch=2)

        # Right content (settings)
        right_content = QWidget()
        right_layout = QVBoxLayout()
        right_content.setLayout(right_layout)

        # Add folder widgets
        self.movie_folder_widgets = []
        settings = load_settings()
        for folder_data in settings.get("custom_folders", []):
            if folder_data.get("sort_type") == "Movies":
                widget = MovieDestinationWidget(folder_data, settings)
                self.movie_folder_widgets.append(widget)
                right_layout.addWidget(widget)

        # Add button and save button layout
        button_layout = QHBoxLayout()

        # Add button
        add_folder_btn = QPushButton("Add New Sort Destination")
        add_folder_btn.clicked.connect(lambda: self.add_new_movie_destination(right_layout))
        button_layout.addWidget(add_folder_btn)

        # Save button
        save_btn = QPushButton("Save Settings")
        save_btn.clicked.connect(lambda: self.save_all_settings())
        button_layout.addWidget(save_btn)

        right_layout.addLayout(button_layout)
        right_layout.addStretch()

        # Add the right content to the main content layout
        content_layout.addWidget(right_content, stretch=1)

        # Add the content layout to the main layout
        layout.addLayout(content_layout)

        return tab

    def save_all_settings(self):
        """Save all current settings"""
        settings = load_settings()

        # Update movie folders
        movie_folders = []
        for widget in self.movie_folder_widgets:
            movie_folders.append(widget.folder_data)

        # Combine all folders
        settings["custom_folders"] = movie_folders

        # Save settings
        if save_settings(settings):
            QMessageBox.information(self, "Success", "Settings saved successfully!")
        else:
            QMessageBox.warning(self, "Error", "Failed to save settings!")

    def add_new_tv_destination(self, layout):
        """Add a new TV show destination widget"""
        from tv_show_code.tv_show_destinations import TVShowDestinationWidget, create_new_tv_destination

        # Create new destination data
        new_destination = create_new_tv_destination()

        # Create widget
        widget = TVShowDestinationWidget(new_destination, self.settings)
        self.folder_widgets.append(widget)

        # Add widget to layout before the add button and stretch
        layout.insertWidget(layout.count() - 2, widget)

        # Save to settings
        settings = load_settings()
        if "custom_folders" not in settings:
            settings["custom_folders"] = []
        settings["custom_folders"].append(new_destination)
        save_settings(settings)

    def add_new_movie_destination(self, layout):
        """Add a new movie destination widget"""
        from movie_code.movie_destinations import MovieDestinationWidget, create_new_movie_destination

        # Create new destination data
        new_destination = create_new_movie_destination()

        # Create widget
        widget = MovieDestinationWidget(new_destination, self.settings)
        self.movie_folder_widgets.append(widget)

        # Add widget to layout before the add button and stretch
        layout.insertWidget(layout.count() - 2, widget)

        # Save to settings
        settings = load_settings()
        if "custom_folders" not in settings:
            settings["custom_folders"] = []
        settings["custom_folders"].append(new_destination)
        save_settings(settings)

    def sort_files(self):
        """Sort TV show files using the TV show sorting implementation"""
        try:
            tv_tab = self.tabs.widget(0)  # Get the TV Shows tab
            file_paths = tv_tab.file_list.file_paths
            if not file_paths:
                QMessageBox.information(self, "No Files", "No TV shows to sort")
                return

            # Show progress dialog
            progress = QProgressDialog("Sorting TV shows...", None, 0, len(file_paths), self)
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setValue(0)

            def update_progress(current, total, message=""):
                if progress.wasCanceled():
                    return
                progress.setValue(current)
                if message:
                    progress.setLabelText(message)

            try:
                # Use the TV show sorting logic
                total, sorted_count, unsorted_count = process_files(file_paths)

                # Clear the list and show results
                tv_tab.file_list.clear()
                QMessageBox.information(
                    self,
                    "TV Show Sorting Complete",
                    f"Successfully sorted {sorted_count} TV shows.\n"
                    f"Unsorted files: {unsorted_count}\n\n"
                    f"Unsorted files can be found in the 0.2 Unsorted Folder"
                )

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error sorting TV shows: {str(e)}")
                logging.error(f"Error in sort_files: {str(e)}")
            finally:
                progress.close()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Unexpected error: {str(e)}")
            logging.error(f"Unexpected error in sort_files: {str(e)}")

    def sort_movies(self):
        """Sort movies using the movie sorting implementation"""
        try:
            movie_paths = self.movie_list.file_paths
            if not movie_paths:
                QMessageBox.information(self, "No Movies", "No movies to sort")
                return

            # Show progress dialog
            progress = QProgressDialog("Sorting movies...", None, 0, len(movie_paths), self)
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setValue(0)

            def update_progress(current, total, message=""):
                if progress.wasCanceled():
                    return
                progress.setValue(current)
                if message:
                    progress.setLabelText(message)

            try:
                # Use the movie sorting logic
                total, sorted_count, unsorted_count = process_gui_movies(movie_paths)

                # Clear the list and show results
                self.movie_list.clear()
                QMessageBox.information(
                    self,
                    "Movie Sorting Complete",
                    f"Successfully sorted {sorted_count} movies.\n"
                    f"Unsorted files: {unsorted_count}\n\n"
                    f"Unsorted files can be found in the 0.2 Unsorted Folder"
                )

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error sorting movies: {str(e)}")
                logging.error(f"Error in sort_movies: {str(e)}")
            finally:
                progress.close()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Unexpected error: {str(e)}")
            logging.error(f"Unexpected error in sort_movies: {str(e)}")

    def detect_file_type(self, file_path: Path) -> str:
        """Detect whether a file is a TV show, movie, or other file."""
        name = file_path.name.lower()

        # TV Show patterns (expanded)
        tv_patterns = [
            r'[s]\d{1,2}[e]\d{1,2}',  # S01E01 format
            r'season\s*\d+\s*episode\s*\d+',  # Season 1 Episode 1 format
            r'\d{1,2}x\d{1,2}',  # 1x01 format
            r'[e][p]?\d{1,2}',  # E01 or EP01 format
            r'[\[\(]?(?:s|season)\s*\d+[\]\)]?',  # [S1] or (Season 1) format
            r'complete\s+season',  # Complete Season format
            r'season\s*\d+',  # Season 1 format
            r'series\s*\d+',  # Series 1 format
        ]

        # Movie patterns (expanded)
        movie_patterns = [
            r'\(\d{4}\)',  # (2023) format
            r'\[\d{4}\]',  # [2023] format
            r'\d{4}.*\b(?:1080p|720p|2160p|4k|uhd|bluray|brrip|dvdrip|web-?dl|webrip|hdrip)\b',  # 2023.1080p format
            r'\b(?:1080p|720p|2160p|4k|uhd|bluray|brrip|dvdrip|web-?dl|webrip|hdrip).*\d{4}\b',  # 1080p.2023 format
            r'[\[\(]\s*\d{4}\s*[\]\)]',  # [ 2023 ] format
            r'\b\d{4}\b.*\b(?:extended|directors\.?cut|theatrical|final\.?cut)\b',  # 2023 Extended Cut format
            r'\bmovie\b.*\d{4}\b',  # Movie 2023 format
        ]

        # Check for TV show patterns
        for pattern in tv_patterns:
            if re.search(pattern, name, re.IGNORECASE):
                return "TV Shows"

        # Check for movie patterns
        for pattern in movie_patterns:
            if re.search(pattern, name, re.IGNORECASE):
                return "Movies"

        # If no specific pattern is matched, return Other Files
        return "Other Files"

    def create_settings_tab(self):
        """Create and add the settings tab"""
        settings_tab = QWidget()
        layout = QVBoxLayout()
        settings_tab.setLayout(layout)

        # Add section title
        title = QLabel("Settings")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title)

        # TMDB API Settings
        api_section = QFrame()
        api_layout = QVBoxLayout()
        api_section.setLayout(api_layout)

        api_title = QLabel("TMDB API")
        api_title.setFont(QFont("Arial", 14))
        api_layout.addWidget(api_title)

        api_status = QLabel()
        if 'tmdb_api_key' in self.settings:
            api_status.setText("✅ API key configured")
            api_status.setStyleSheet("color: #4CAF50;")
        else:
            api_status.setText("❌ API key not configured")
            api_status.setStyleSheet("color: #f44336;")
        api_layout.addWidget(api_status)

        api_button = QPushButton("Configure API Key")
        api_button.clicked.connect(self.configure_tmdb_api)
        api_layout.addWidget(api_button)

        layout.addWidget(api_section)

        # Theme Settings
        theme_section = QFrame()
        theme_layout = QVBoxLayout()
        theme_section.setLayout(theme_layout)

        theme_title = QLabel("Theme Settings")
        theme_title.setFont(QFont("Arial", 14))
        theme_layout.addWidget(theme_title)

        theme_button = QPushButton("Toggle Dark/Light Mode")
        theme_button.clicked.connect(self.toggle_theme)
        theme_layout.addWidget(theme_button)

        layout.addWidget(theme_section)

        # Add stretch to push everything to the top
        layout.addStretch()

        # Style
        settings_tab.setStyleSheet("""
            QFrame {
                background: rgb(20, 20, 20);
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
            QLabel {
                color: white;
            }
            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.2);
            }
        """)

        return settings_tab

    def change_folder(self, folder_type):
        """Change the folder path for movies or TV shows"""
        folder = QFileDialog.getExistingDirectory(self, f"Select {folder_type.title()} Folder")
        if folder:
            if folder_type == 'movies':
                # Update settings
                self.settings['movies_folder'] = folder
                self.save_settings()

                # Update movie library
                if hasattr(self, 'movie_library_window') and self.movie_library_window:
                    self.movie_library_window.set_movie_folder(folder)

                QMessageBox.information(self, "Success", f"{folder_type.title()} folder changed to: {folder}")

    def save_settings(self):
        """Save settings to file"""
        try:
            settings_file = Path(__file__).parent / "settings.json"
            with open(settings_file, 'w') as f:
                json.dump(self.settings, f, indent=4)
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to save settings: {str(e)}")

    # LOGS TAB CREATION:
    # Creates the logs tab that displays application logs
    def create_logs_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)
        self.logs_display = QTextEdit()
        self.logs_display.setReadOnly(True)
        layout.addWidget(self.logs_display)
        return tab

    # MOVIES TAB CREATION:
    # Creates the tab for handling movie files specifically
    def create_movies_tab(self):
        """Creates a dedicated tab for movie sorting"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Create header with title and description
        header = QFrame()
        header_layout = QVBoxLayout()
        header.setLayout(header_layout)

        title = QLabel("Movie Sorting")
        title.setStyleSheet("font-size: 18px; font-weight: bold;")
        header_layout.addWidget(title)

        description = QLabel("Sort your movie files into organized folders automatically.")
        description.setWordWrap(True)
        header_layout.addWidget(description)

        layout.addWidget(header)

        # Create a horizontal layout for destinations and file drop area
        content_layout = QHBoxLayout()

        # Create folder toggles section with scroll area
        folders_group = QGroupBox("Movie Sort Destinations")
        folders_group.setMinimumWidth(300)
        folders_group.setMaximumWidth(400)
        folders_layout = QVBoxLayout()
        folders_group.setLayout(folders_layout)

        # Create scroll area for destinations
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create a widget to hold all folder widgets
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout()
        scroll_content.setLayout(scroll_layout)

        # Import the movie destinations module
        from movie_code.movie_destinations import MovieDestinationWidget, get_movie_destinations, create_new_movie_destination

        # Load movie destinations
        movie_destinations = get_movie_destinations()

        # Create folder toggle widgets
        self.movie_folder_widgets = []
        for folder_data in movie_destinations:
            folder_widget = MovieDestinationWidget(folder_data, self.settings)
            self.movie_folder_widgets.append(folder_widget)
            scroll_layout.addWidget(folder_widget)

        # Add button to create new folder
        add_folder_btn = QPushButton("+ Add New Movie Sort Destination")
        add_folder_btn.clicked.connect(lambda: self.add_new_movie_destination(scroll_layout))
        scroll_layout.addWidget(add_folder_btn)

        # Add stretch to push everything to the top
        scroll_layout.addStretch()

        # Set the scroll content
        scroll_area.setWidget(scroll_content)
        folders_layout.addWidget(scroll_area)

        # Add the folders group to the content layout
        content_layout.addWidget(folders_group)

        # Create right side content for file drop
        right_content = QWidget()
        right_layout = QVBoxLayout()
        right_content.setLayout(right_layout)

        # Instructions
        drop_label = QLabel("Drag and drop movie files here or use the 'Add Files' button")
        drop_label.setAlignment(Qt.AlignCenter)
        right_layout.addWidget(drop_label)

        # File list for movies
        self.movie_list = MovieListWidget()
        self.movie_list.set_main_window(self)
        right_layout.addWidget(self.movie_list)

        # Progress bar
        self.movie_progress_bar = QProgressBar()
        self.movie_progress_bar.setValue(0)
        right_layout.addWidget(self.movie_progress_bar)

        # Buttons
        buttons_layout = QHBoxLayout()

        add_files_button = QPushButton("Add Movies")
        add_files_button.clicked.connect(self.add_movie_files)
        buttons_layout.addWidget(add_files_button)

        sort_button = QPushButton("Sort Movies")
        sort_button.clicked.connect(self.sort_movies)
        buttons_layout.addWidget(sort_button)

        right_layout.addLayout(buttons_layout)

        # Add the right content to the main content layout
        content_layout.addWidget(right_content, stretch=1)

        # Add the content layout to the main layout
        layout.addLayout(content_layout)

        return tab

    def add_new_movie_destination(self, layout):
        """Add a new movie destination widget"""
        from movie_code.movie_destinations import MovieDestinationWidget, create_new_movie_destination

        # Create new destination data
        new_destination = create_new_movie_destination()

        # Create widget
        widget = MovieDestinationWidget(new_destination, self.settings)
        self.movie_folder_widgets.append(widget)

        # Add widget to layout before the add button and stretch
        layout.insertWidget(layout.count() - 2, widget)

        # Save to settings
        settings = load_settings()
        if "custom_folders" not in settings:
            settings["custom_folders"] = []
        settings["custom_folders"].append(new_destination)
        save_settings(settings)

    def create_search_tab(self):
        """Create the search tab for finding media files."""
        search_tab = QWidget()
        layout = QVBoxLayout(search_tab)
        layout.setSpacing(8)
        layout.setContentsMargins(10, 10, 10, 10)

        # Use a dark theme with white text for better visibility
        search_tab.setStyleSheet("""
            QWidget {
                background-color: #2d2d2d;
                color: #ffffff;
                font-size: 12px;
            }
            QLabel {
                color: #ffffff;
                font-size: 12px;
            }
            QLineEdit {
                padding: 6px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #3d3d3d;
                color: #ffffff;
                font-size: 12px;
            }
            QPushButton {
                padding: 6px 12px;
                background-color: #0078d7;
                color: #ffffff;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QComboBox {
                padding: 4px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #3d3d3d;
                color: #ffffff;
                font-size: 12px;
            }
            QComboBox QAbstractItemView {
                background-color: #3d3d3d;
                color: #ffffff;
                selection-background-color: #0078d7;
            }
            QListWidget {
                background-color: #3d3d3d;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                font-size: 12px;
                alternate-background-color: #353535;
            }
            QListWidget::item {
                padding: 4px;
                border-bottom: 1px solid #444444;
            }
            QListWidget::item:selected {
                background: #0078d7;
                color: #ffffff;
            }
            QFrame {
                background-color: #2d2d2d;
                border: 1px solid #444444;
                border-radius: 4px;
            }
        """)

        # Simple header
        header_layout = QHBoxLayout()
        title_label = QLabel("Media Search")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #ffffff;")
        header_layout.addWidget(title_label)
        layout.addLayout(header_layout)

        # Search input
        search_layout = QHBoxLayout()

        search_label = QLabel("Search:")
        search_label.setStyleSheet("color: #ffffff;")
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search for files...")
        search_layout.addWidget(self.search_input, 1)

        search_button = QPushButton("Search")
        search_button.clicked.connect(lambda: self.filter_search_results(self.search_input.text()))
        search_layout.addWidget(search_button)

        layout.addLayout(search_layout)

        # Filters
        filters_frame = QFrame()
        filters_layout = QHBoxLayout(filters_frame)
        filters_layout.setContentsMargins(5, 5, 5, 5)

        # File type filter
        type_label = QLabel("Type:")
        type_label.setStyleSheet("color: #ffffff;")
        filters_layout.addWidget(type_label)

        self.type_combo = QComboBox()
        self.type_combo.addItems(["All Types", "Movies", "TV Shows", "Anime", "Videos", "Subtitles", "Images", "Documents"])
        self.type_combo.currentTextChanged.connect(self.refresh_search)  # Connect to refresh_search instead
        filters_layout.addWidget(self.type_combo)

        # Size filter
        size_label = QLabel("Size:")
        size_label.setStyleSheet("color: #ffffff;")
        filters_layout.addWidget(size_label)

        self.size_combo = QComboBox()
        self.size_combo.addItems(["Any Size", "< 100 MB", "100 MB - 1 GB", "> 1 GB"])
        self.size_combo.currentTextChanged.connect(self.update_search_results)
        filters_layout.addWidget(self.size_combo)

        # Sort options
        sort_label = QLabel("Sort by:")
        sort_label.setStyleSheet("color: #ffffff;")
        filters_layout.addWidget(sort_label)

        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["Name", "Date", "Size", "Type"])
        self.sort_combo.currentTextChanged.connect(self.update_search_results)
        filters_layout.addWidget(self.sort_combo)

        layout.addWidget(filters_frame)

        # Action buttons
        action_frame = QFrame()
        action_layout = QHBoxLayout(action_frame)
        action_layout.setContentsMargins(5, 5, 5, 5)

        # Refresh button
        refresh_btn = QPushButton("Refresh Search")
        refresh_btn.clicked.connect(self.refresh_search)
        action_layout.addWidget(refresh_btn)

        # Fetch Media Info button
        fetch_info_btn = QPushButton("Fetch Media Info")
        fetch_info_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: #ffffff;
            }
        """)
        fetch_info_btn.clicked.connect(self.fetch_media_info)
        action_layout.addWidget(fetch_info_btn)

        # Fetch Extras button
        fetch_extras_btn = QPushButton("Fetch Extras")
        fetch_extras_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: #ffffff;
            }
        """)
        fetch_extras_btn.clicked.connect(self.fetch_media_extras)
        action_layout.addWidget(fetch_extras_btn)

        layout.addWidget(action_frame)

        # Search results
        results_frame = QFrame()
        results_layout = QVBoxLayout(results_frame)
        results_layout.setContentsMargins(5, 5, 5, 5)

        # Results header with count
        results_header = QHBoxLayout()
        results_label = QLabel("Search Results:")
        results_label.setStyleSheet("font-weight: bold; color: #ffffff;")
        results_count = QLabel("0 items")
        results_count.setObjectName("results_count")
        results_count.setStyleSheet("color: #ffffff;")
        results_header.addWidget(results_label)
        results_header.addStretch()
        results_header.addWidget(results_count)
        results_layout.addLayout(results_header)

        # Results list with black background and white text for all items
        self.search_results = QListWidget()
        self.search_results.setSelectionMode(QAbstractItemView.SingleSelection)
        self.search_results.itemDoubleClicked.connect(self.open_search_result)
        # Turn off alternating row colors since we want all rows to be black
        self.search_results.setAlternatingRowColors(False)
        # Apply explicit styling for black background with white text
        self.search_results.setStyleSheet("""
            QListWidget {
                background-color: #000000 !important;
                color: #ffffff !important;
                font-size: 12px;
                border: 1px solid #444444;
            }
            QListWidget::item {
                color: #ffffff !important;
                background-color: #000000 !important;
                padding: 4px;
                border-bottom: 1px solid #333333;
            }
            QListWidget::item:selected {
                background-color: #0078d7 !important;
                color: #ffffff !important;
            }
            QListWidget::item:hover:!selected {
                background-color: #333333 !important;
                color: #ffffff !important;
            }
        """)
        results_layout.addWidget(self.search_results)

        # Store the results count label for updates
        self.results_count_label = results_count

        # Action buttons for results
        results_actions = QHBoxLayout()

        open_button = QPushButton("Open Selected")
        open_button.clicked.connect(lambda: self.open_search_result(self.search_results.currentItem())
                                   if self.search_results.currentItem() else None)
        results_actions.addWidget(open_button)

        # Add a button to open containing folder
        open_folder_button = QPushButton("Open Containing Folder")
        open_folder_button.clicked.connect(lambda: self.open_containing_folder(self.search_results.currentItem())
                                         if self.search_results.currentItem() else None)
        results_actions.addWidget(open_folder_button)

        results_layout.addLayout(results_actions)

        layout.addWidget(results_frame)

        # Initialize TMDB helper
        self.tmdb = TMDBHelper()

        # Initial search refresh
        self.refresh_search()

        return search_tab

    def open_containing_folder(self, item):
        """Open the folder containing the selected file"""
        if not item:
            return

        try:
            file_path = Path(item.data(Qt.UserRole))
            if file_path.exists():
                # Open the parent folder
                folder_path = file_path.parent
                if sys.platform == 'win32':
                    os.startfile(str(folder_path))
                elif sys.platform == 'darwin':
                    subprocess.run(['open', str(folder_path)])
                else:
                    subprocess.run(['xdg-open', str(folder_path)])
            else:
                QMessageBox.warning(self, "Error", "File not found.")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open folder: {e}")

    def create_media_library_tab(self):
        """Create and add the media library tab"""
        from .media_library.media_library_tab import MediaLibraryTab
        media_library_tab = MediaLibraryTab(self)
        self.tabs.addTab(media_library_tab, "Media Library")

    def create_status_tab(self):
        """Creates the status tab that displays sorting statistics and system health"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Create a splitter for statistics and health
        stats_group = QGroupBox("Statistics")
        stats_layout = QVBoxLayout()
        stats_group.setLayout(stats_layout)

        # Status display
        self.status_text = QLabel()
        self.status_text.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        self.status_text.setWordWrap(True)
        stats_layout.addWidget(self.status_text)

        # Add stats group to main layout
        layout.addWidget(stats_group)

        # Health status group
        health_group = QGroupBox("System Health Status")
        health_layout = QVBoxLayout()
        health_group.setLayout(health_layout)

        # Create grid layout for health indicators
        grid = QGridLayout()

        # Initialize dictionaries for health indicators
        self.health_indicators = {}
        self.health_messages = {}

        # Add basic health checks
        health_checks = [
            ("Storage", "Checking available space..."),
            ("Database", "Checking database status..."),
            ("Network", "Checking network connectivity..."),
            ("API", "Checking TMDB API status...")
        ]

        for row, (component, message) in enumerate(health_checks):
            # Create indicator label (colored circle)
            indicator = QLabel("⬤")  # Unicode circle
            indicator.setStyleSheet("color: gray;")  # Default to gray

            # Create component name label
            name_label = QLabel(f"{component}:")

            # Create message label
            message_label = QLabel(message)
            message_label.setWordWrap(True)

            # Store references
            self.health_indicators[component] = indicator
            self.health_messages[component] = message_label

            # Add to grid
            grid.addWidget(indicator, row, 0)
            grid.addWidget(name_label, row, 1)
            grid.addWidget(message_label, row, 2)

        health_layout.addLayout(grid)

        # Add health group to main layout
        layout.addWidget(health_group)

        # Refresh button
        refresh_button = QPushButton("Refresh Status")
        refresh_button.clicked.connect(self.update_status_display)
        layout.addWidget(refresh_button)

        # Add stretch to push everything to the top
        layout.addStretch()

        # Initial status update
        self.update_status_display()

        return tab

    def update_status_display(self):
        """Update the status display with current statistics and health status"""
        try:
            # Update statistics
            movies_folder = get_movies_folder()
            if movies_folder and movies_folder.exists():
                movie_count = sum(1 for _ in movies_folder.glob("**/*.mp4"))
                status_text = f"Movies in library: {movie_count}\n"
            else:
                status_text = "Movies folder not configured\n"

            self.status_text.setText(status_text)

            # Update health indicators
            self._update_storage_health()
            self._update_database_health()
            self._update_network_health()
            self._update_api_health()

        except Exception as e:
            self.log_action(f"Error updating status: {e}")
            self.status_text.setText("Error loading status information")

    def _update_storage_health(self):
        """Update storage health status"""
        try:
            movies_folder = get_movies_folder()
            if movies_folder and movies_folder.exists():
                total, used, free = shutil.disk_usage(str(movies_folder))
                free_gb = free / (1024**3)  # Convert to GB

                if free_gb > 50:  # More than 50GB free
                    self._set_health_status("Storage", "good", f"Available space: {free_gb:.1f} GB")
                elif free_gb > 10:  # More than 10GB free
                    self._set_health_status("Storage", "warning", f"Low space: {free_gb:.1f} GB remaining")
                else:  # Less than 10GB free
                    self._set_health_status("Storage", "error", f"Critical: Only {free_gb:.1f} GB remaining")
            else:
                self._set_health_status("Storage", "error", "Movies folder not configured")
        except Exception as e:
            self._set_health_status("Storage", "error", f"Error checking storage: {e}")

    def _update_database_health(self):
        """Update database health status"""
        try:
            cache_file = Path(__file__).parent / "movie_cache.json"
            if cache_file.exists():
                self._set_health_status("Database", "good", "Movie cache is available")
            else:
                self._set_health_status("Database", "warning", "Movie cache not found")
        except Exception as e:
            self._set_health_status("Database", "error", f"Error checking database: {e}")

    def _update_network_health(self):
        """Update network health status"""
        try:
            # Simple network check (ping google.com)
            result = subprocess.run(["ping", "-n", "1", "google.com"],
                                 capture_output=True,
                                 text=True)
            if result.returncode == 0:
                self._set_health_status("Network", "good", "Internet connection available")
            else:
                self._set_health_status("Network", "error", "No internet connection")
        except Exception as e:
            self._set_health_status("Network", "error", f"Error checking network: {e}")

    def _update_api_health(self):
        """Update API health status"""
        if 'tmdb_api_key' in self.settings:
            self._set_health_status("API", "good", "TMDB API key configured")
        else:
            self._set_health_status("API", "warning", "TMDB API key not configured")

    def _set_health_status(self, component, status, message):
        """Set the health status for a component"""
        if component in self.health_indicators:
            color = {
                "good": "green",
                "warning": "orange",
                "error": "red"
            }.get(status, "gray")

            self.health_indicators[component].setStyleSheet(f"color: {color};")
            self.health_messages[component].setText(message)

    def fetch_media_info(self):
        """Fetch comprehensive media information for selected files"""
        try:
            if not self.tmdb.initialize():
                QMessageBox.warning(
                    self,
                    "API Key Required",
                    "Please configure your TMDB API key first using the 'Configure API' button."
                )
                return

            selected_items = self.search_results.selectedItems()
            if not selected_items:
                QMessageBox.information(self, "No Selection", "Please select media files to fetch information for.")
                return

            progress = QProgressDialog("Fetching media information...", "Cancel", 0, len(selected_items), self)
            progress.setWindowModality(Qt.WindowModal)

            for idx, item in enumerate(selected_items):
                if progress.wasCanceled():
                    break

                file_path = Path(item.data(Qt.UserRole))
                if not self.is_media_file(file_path):
                    continue

                progress.setLabelText(f"Processing: {file_path.name}")
                progress.setValue(idx)

                try:
                    # Clean filename for better search results
                    search_name = self.clean_filename_for_search(file_path.stem)

                    # Search for media
                    results = self.tmdb.search_media(search_name)

                    if results and results.get('results'):
                        # Get the first result
                        media = results['results'][0]
                        media_type = media.get('media_type', 'movie')

                        # Get detailed information
                        details = self.tmdb.get_details(media['id'], media_type)

                        if details:
                            # Create media info folder
                            info_folder = file_path.parent / f"{file_path.stem}_info"
                            info_folder.mkdir(exist_ok=True)

                            # Save media information
                            info_file = info_folder / "media_info.json"
                            with open(info_file, 'w', encoding='utf-8') as f:
                                json.dump(details, f, indent=2)

                            # Download images
                            self.download_media_images(details, info_folder)

                            # Create an HTML summary
                            self.create_html_summary(details, info_folder)

                            self.log_action(f"Fetched information for: {file_path.name}")
                    else:
                        self.log_action(f"No results found for: {file_path.name}")

                except Exception as e:
                    self.log_action(f"Error fetching info for {file_path.name}: {e}")

            progress.setValue(len(selected_items))
            QMessageBox.information(self, "Complete", "Finished fetching media information.")

        except Exception as e:
            self.log_action(f"Error in fetch_media_info: {e}")
            QMessageBox.critical(self, "Error", f"Failed to fetch media information: {str(e)}")

    def download_media_images(self, details, folder):
        """Download various images for the media"""
        try:
            # Create subfolders for different image types
            poster_folder = folder / "posters"
            backdrop_folder = folder / "backdrops"
            still_folder = folder / "stills"
            logo_folder = folder / "logos"

            for image_folder in [poster_folder, backdrop_folder, still_folder, logo_folder]:
                image_folder.mkdir(exist_ok=True)

            # Download main poster and backdrop
            if details.get('poster_path'):
                poster_url = self.tmdb.get_image_url(details['poster_path'])
                self.download_image(poster_url, poster_folder / "main_poster.jpg")

            if details.get('backdrop_path'):
                backdrop_url = self.tmdb.get_image_url(details['backdrop_path'])
                self.download_image(backdrop_url, backdrop_folder / "main_backdrop.jpg")

            # Download additional images
            if 'images' in details:
                # Additional posters (limit to 5)
                for idx, poster in enumerate(details['images'].get('posters', [])[:5]):
                    url = self.tmdb.get_image_url(poster['file_path'])
                    self.download_image(url, poster_folder / f"poster_{idx + 1}.jpg")

                # Additional backdrops (limit to 3)
                for idx, backdrop in enumerate(details['images'].get('backdrops', [])[:3]):
                    url = self.tmdb.get_image_url(backdrop['file_path'])
                    self.download_image(url, backdrop_folder / f"backdrop_{idx + 1}.jpg")

                # Episode stills for TV shows (limit to 10)
                if 'stills' in details['images']:
                    for idx, still in enumerate(details['images'].get('stills', [])[:10]):
                        url = self.tmdb.get_image_url(still['file_path'])
                        self.download_image(url, still_folder / f"still_{idx + 1}.jpg")

                # Logos
                for idx, logo in enumerate(details['images'].get('logos', [])[:2]):
                    url = self.tmdb.get_image_url(logo['file_path'])
                    self.download_image(url, logo_folder / f"logo_{idx + 1}.png")

        except Exception as e:
            self.log_action(f"Error downloading images: {e}")

    def download_image(self, url, path):
        """Download an image from URL"""
        try:
            if url:
                response = requests.get(url)
                if response.ok:
                    with open(path, 'wb') as f:
                        f.write(response.content)
        except Exception as e:
            self.log_action(f"Error downloading image {url}: {e}")

    def create_html_summary(self, details, folder):
        """Create a beautiful HTML summary of the media information"""
        try:
            media_type = 'TV Show' if 'number_of_episodes' in details else 'Movie'
            is_anime = self.tmdb.is_likely_anime(details)

            # Get cast and crew information
            cast = details.get('credits', {}).get('cast', [])[:10]  # Top 10 cast members
            crew = details.get('credits', {}).get('crew', [])
            directors = [c for c in crew if c['job'] == 'Director']
            writers = [c for c in crew if c['job'] in ['Writer', 'Screenplay']]

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>{details.get('title', details.get('name', 'Media Info'))}</title>
                <style>
                    body {{
                        font-family: Arial, sans-serif;
                        max-width: 1000px;
                        margin: 0 auto;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }}
                    .media-card {{
                        background: white;
                        border-radius: 10px;
                        padding: 20px;
                        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                        margin-bottom: 20px;
                    }}
                    .title {{
                        color: #2c3e50;
                        font-size: 24px;
                        margin-bottom: 10px;
                    }}
                    .info {{
                        color: #34495e;
                        margin: 10px 0;
                    }}
                    .overview {{
                        line-height: 1.6;
                        color: #7f8c8d;
                    }}
                    .images {{
                        margin-top: 20px;
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                        gap: 20px;
                    }}
                    .image-container {{
                        position: relative;
                    }}
                    .image-container img {{
                        width: 100%;
                        height: auto;
                        border-radius: 5px;
                        transition: transform 0.3s ease;
                    }}
                    .image-container:hover img {{
                        transform: scale(1.05);
                    }}
                    .cast-grid {{
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                        gap: 15px;
                        margin-top: 20px;
                    }}
                    .cast-card {{
                        background: #f8f9fa;
                        border-radius: 5px;
                        padding: 10px;
                        text-align: center;
                    }}
                    .cast-card img {{
                        width: 100px;
                        height: 100px;
                        border-radius: 50%;
                        object-fit: cover;
                    }}
                    .japanese {{
                        font-family: "Noto Sans JP", sans-serif;
                    }}
                </style>
            </head>
            <body>
                <div class="media-card">
                    <h1 class="title">
                        {details.get('title', details.get('name', 'Unknown Title'))}
                        {f'<br><span class="japanese">{details.get("original_title_jp", "")}</span>' if is_anime else ''}
                    </h1>

                    <div class="info">
                        <p><strong>Type:</strong> {'Anime' if is_anime else media_type}</p>
                        <p><strong>Release Date:</strong> {details.get('release_date', details.get('first_air_date', 'Unknown'))}</p>
                        <p><strong>Rating:</strong> {details.get('vote_average', 'N/A')}/10</p>
                        {'<p><strong>Episodes:</strong> ' + str(details.get('number_of_episodes', 'N/A')) + '</p>' if media_type == 'TV Show' else ''}
                        <p><strong>Genres:</strong> {', '.join(g['name'] for g in details.get('genres', []))}</p>

                        {f'''
                        <p><strong>Original Title:</strong> <span class="japanese">{details.get('original_title_jp', 'N/A')}</span></p>
                        <p><strong>Production Companies:</strong> {', '.join(c['name'] for c in details.get('production_companies', []))}</p>
                        ''' if is_anime else ''}
                    </div>

                    <div class="overview">
                        <h3>Overview</h3>
                        <p>{details.get('overview', 'No overview available.')}</p>

                        {f'''
                        <h3>Japanese Overview</h3>
                        <p class="japanese">{details.get('overview_jp', 'No Japanese overview available.')}</p>
                        ''' if is_anime and details.get('overview_jp') else ''}
                    </div>

                    <div class="cast-crew">
                        <h3>Cast & Crew</h3>
                        <div class="cast-grid">
                            {'''
                            <h4>Directors:</h4>
                            {''.join(f'<div class="cast-card"><p>{d["name"]}</p></div>' for d in directors)}

                            <h4>Writers:</h4>
                            {''.join(f'<div class="cast-card"><p>{w["name"]}</p></div>' for w in writers)}

                            <h4>Cast:</h4>
                            {''.join(f"""
                            <div class="cast-card">
                                <img src="{self.tmdb.get_image_url(actor['profile_path']) if actor.get('profile_path') else 'https://via.placeholder.com/100'}" alt="{actor['name']}">
                                <p><strong>{actor['name']}</strong></p>
                                <p>{actor['character']}</p>
                            </div>
                            """ for actor in cast)}
                            '''}
                        </div>
                    </div>

                    <div class="images">
                        <h3>Images</h3>
                        {''.join(f"""
                        <div class="image-container">
                            <img src="posters/poster_{i}.jpg" alt="Poster {i}" title="Poster {i}">
                        </div>
                        """ for i in range(1, 6) if (folder / f"posters/poster_{i}.jpg").exists())}

                        {''.join(f"""
                        <div class="image-container">
                            <img src="backdrops/backdrop_{i}.jpg" alt="Backdrop {i}" title="Backdrop {i}">
                        </div>
                        """ for i in range(1, 4) if (folder / f"backdrops/backdrop_{i}.jpg").exists())}

                        {''.join(f"""
                        <div class="image-container">
                            <img src="stills/still_{i}.jpg" alt="Still {i}" title="Still {i}">
                        </div>
                        """ for i in range(1, 11) if (folder / f"stills/still_{i}.jpg").exists())}
                    </div>
                </div>
            </body>
            </html>
            """

            with open(folder / "summary.html", 'w', encoding='utf-8') as f:
                f.write(html)

        except Exception as e:
            self.log_action(f"Error creating HTML summary: {e}")

    def fetch_media_extras(self):
        """Fetch subtitles and images for selected media files"""
        try:
            selected_items = self.search_results.selectedItems()
            if not selected_items:
                QMessageBox.information(self, "No Selection", "Please select media files to fetch subtitles and images for.")
                return

            # Import required modules
            try:
                import requests
                from bs4 import BeautifulSoup
            except ImportError:
                self.log_action("Installing required packages...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "requests", "beautifulsoup4"])
                import requests
                from bs4 import BeautifulSoup

            progress = QProgressDialog("Fetching subtitles and images...", "Cancel", 0, len(selected_items), self)
            progress.setWindowModality(Qt.WindowModal)

            for idx, item in enumerate(selected_items):
                if progress.wasCanceled():
                    break

                file_path = Path(item.data(Qt.UserRole))
                if not self.is_media_file(file_path):
                    continue

                progress.setLabelText(f"Processing: {file_path.name}")
                progress.setValue(idx)

                try:
                    # Create a folder for extras if it doesn't exist
                    extras_folder = file_path.parent / "extras"
                    extras_folder.mkdir(exist_ok=True)

                    # Clean the filename for searching
                    search_name = self.clean_filename_for_search(file_path.stem)

                    # Fetch subtitles
                    self.fetch_subtitles(search_name, extras_folder)

                    # Fetch images (posters, thumbnails)
                    self.fetch_images(search_name, extras_folder)

                    self.log_action(f"Fetched extras for: {file_path.name}")

                except Exception as e:
                    self.log_action(f"Error fetching extras for {file_path.name}: {e}")

            progress.setValue(len(selected_items))
            self.refresh_search()  # Refresh to show new files
            QMessageBox.information(self, "Complete", "Finished fetching subtitles and images.")

        except Exception as e:
            self.log_action(f"Error in fetch_media_extras: {e}")
            QMessageBox.critical(self, "Error", f"Failed to fetch extras: {str(e)}")

    def is_media_file(self, file_path):
        """Check if the file is a media file that can have subtitles/images"""
        media_extensions = {'.mp4', '.mkv', '.avi', '.mov', '.wmv'}
        return file_path.suffix.lower() in media_extensions

    def clean_filename_for_search(self, filename):
        """Clean filename for better search results"""
        # Remove season and episode information
        filename = re.sub(r'S\d{1,2}E\d{1,2}|Season\s*\d+\s*Episode\s*\d+|\s*-\s*E\d{1,2}|\s*-\s*EP\d{1,2}', '', filename, flags=re.IGNORECASE)

        # Remove year if present
        filename = re.sub(r'\(\d{4}\)', '', filename)

        # Remove quality indicators
        filename = re.sub(r'\b(720p|1080p|2160p|4K|HDR|BLURAY|WEB-DL|x264|x265|HEVC)\b', '', filename, flags=re.IGNORECASE)

        # Remove special characters
        filename = re.sub(r'[._-]', ' ', filename)

        # Remove extra spaces
        filename = ' '.join(filename.split())

        # If it's a TV show with a dash separator, take only the show name part
        if ' - ' in filename:
            filename = filename.split(' - ')[0]

        return filename.strip()

    def fetch_subtitles(self, search_name, extras_folder):
        """Fetch subtitles from various sources"""
        try:
            # This is a placeholder for the actual implementation
            # You would need to integrate with subtitle APIs like OpenSubtitles
            self.log_action(f"Searching for subtitles: {search_name}")

            # Example of how to save a subtitle file (implementation needed)
            # subtitle_path = extras_folder / f"{search_name}.srt"
            # with open(subtitle_path, 'w', encoding='utf-8') as f:
            #     f.write(subtitle_content)

        except Exception as e:
            self.log_action(f"Error fetching subtitles: {e}")

    def fetch_images(self, search_name, extras_folder):
        """Fetch images (posters, thumbnails) from various sources"""
        try:
            # This is a placeholder for the actual implementation
            # You would need to integrate with media APIs like TMDB or IMDB
            self.log_action(f"Searching for images: {search_name}")

            # Example of how to save an image (implementation needed)
            # image_path = extras_folder / f"{search_name}-poster.jpg"
            # with open(image_path, 'wb') as f:
            #     f.write(image_content)

        except Exception as e:
            self.log_action(f"Error fetching images: {e}")

    def refresh_search(self):
        """Refresh the search results by scanning media directories."""
        # Show a progress dialog while scanning
        progress_dialog = QProgressDialog("Scanning for media files...", "Cancel", 0, 100, self)
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.show()
        QApplication.processEvents()  # Ensure the dialog is displayed

        # Clear existing results
        self.search_results.clear()

        # Track seen files to avoid duplicates
        seen_files = set()

        # Get configured media directories
        movies_folder = get_movies_folder()

        # Also search the MediaSorter root directory
        root_folder = Path(__file__).resolve().parent.parent

        # Add all relevant directories to search
        media_dirs = []
        if movies_folder and movies_folder.exists():
            media_dirs.append(movies_folder)

        # Always include the root folder
        media_dirs.append(root_folder)

        # Add TV Shows folder if it exists
        tv_shows_folder = self.get_tv_shows_folder()
        if tv_shows_folder and tv_shows_folder.exists():
            media_dirs.append(tv_shows_folder)

        self.log_action(f"Scanning directories for media files: {', '.join(str(d) for d in media_dirs)}")
        progress_dialog.setValue(10)  # Update progress

        # Get the current filter type
        current_filter = self.type_combo.currentText() if hasattr(self, 'type_combo') else "All Types"

        # Scan directories for media files
        total_dirs = len(media_dirs)
        for dir_idx, media_dir in enumerate(media_dirs):
            if progress_dialog.wasCanceled():
                break

            if not media_dir or not os.path.exists(media_dir):
                continue

            self.log_action(f"Scanning directory: {media_dir}")
            progress_value = 10 + int((dir_idx / total_dirs) * 80)  # Progress from 10% to 90%
            progress_dialog.setValue(progress_value)
            progress_dialog.setLabelText(f"Scanning: {media_dir}")
            QApplication.processEvents()  # Keep UI responsive

            # Scan all files and folders
            for root, dirs, files in os.walk(media_dir):
                if progress_dialog.wasCanceled():
                    break

                # Skip hidden directories and system directories
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]

                # Process all files in the current directory
                for file in files:
                    # Include more file types for comprehensive search
                    file_path = Path(os.path.join(root, file))

                    # Skip if we've seen this filename or if it's a hidden file
                    if file_path.name in seen_files or file_path.name.startswith('.'):
                        continue

                    # Determine file type
                    file_type = self.get_file_type(file_path)

                    # Apply type filter if not "All Types"
                    if current_filter != "All Types" and file_type != current_filter:
                        continue

                    # Add the file to our results
                    seen_files.add(file_path.name)

                    # Create list item with file type info
                    item = QListWidgetItem(f"{file_path.name} ({file_type})")
                    item.setData(Qt.UserRole, str(file_path))
                    self.search_results.addItem(item)

        # Sort items alphabetically
        self.search_results.sortItems()
        progress_dialog.setValue(95)  # Almost done

        # Update the results count label if it exists
        if hasattr(self, 'results_count_label') and self.results_count_label is not None:
            count = self.search_results.count()
            self.results_count_label.setText(f"{count} items")

        # Log the number of files found
        self.log_action(f"Found {self.search_results.count()} files in search")

        # Connect search input (only connect once)
        try:
            self.search_input.textChanged.disconnect(self.filter_search_results)
        except:
            pass  # If not connected yet, that's fine
        self.search_input.textChanged.connect(self.filter_search_results)

        # Ensure styling is applied to all items
        self.ensure_search_results_styling()

        # Close progress dialog
        progress_dialog.setValue(100)
        progress_dialog.close()

    def get_tv_shows_folder(self):
        """Get the TV Shows folder from settings"""
        try:
            settings = load_settings()
            for folder_data in settings.get("custom_folders", []):
                if folder_data.get("sort_type") == "TV Shows":
                    return Path(folder_data.get("path", ""))
            return None
        except Exception as e:
            self.log_action(f"Error getting TV Shows folder: {e}")
            return None

    def ensure_search_results_styling(self):
        """Ensure the search results list has the correct styling"""
        # Apply explicit styling for black background with white text
        self.search_results.setStyleSheet("""
            QListWidget {
                background-color: #000000 !important;
                color: #ffffff !important;
                font-size: 12px;
                border: 1px solid #444444;
            }
            QListWidget::item {
                color: #ffffff !important;
                background-color: #000000 !important;
                padding: 4px;
                border-bottom: 1px solid #333333;
            }
            QListWidget::item:selected {
                background-color: #0078d7 !important;
                color: #ffffff !important;
            }
            QListWidget::item:hover:!selected {
                background-color: #333333 !important;
                color: #ffffff !important;
            }
        """)

        # Force update of all items to ensure styling is applied
        for i in range(self.search_results.count()):
            item = self.search_results.item(i)
            if item:
                # Get the current text and data
                text = item.text()
                data = item.data(Qt.UserRole)

                # Explicitly set the text color for each item
                item.setForeground(QColor(255, 255, 255))  # White text
                item.setBackground(QColor(0, 0, 0))  # Black background

    def filter_search_results(self, search_text):
        """Filter search results based on search text."""
        if not search_text:
            # If search text is empty, show all items
            for i in range(self.search_results.count()):
                self.search_results.item(i).setHidden(False)

            # Ensure styling is applied
            self.ensure_search_results_styling()
            return

        search_text = search_text.lower()
        search_terms = search_text.split()

        # Get the current filter type
        current_filter = self.type_combo.currentText() if hasattr(self, 'type_combo') else "All Types"

        # Count how many items match
        match_count = 0

        for i in range(self.search_results.count()):
            item = self.search_results.item(i)
            item_text = item.text().lower()
            file_path = Path(item.data(Qt.UserRole))

            # Check if all search terms are in the item text or path
            path_str = str(file_path).lower()

            # Apply type filter if not "All Types"
            if current_filter != "All Types":
                file_type = self.get_file_type(file_path)
                if file_type != current_filter:
                    item.setHidden(True)
                    continue

            # Check if all search terms match either the filename or the path
            matches = all(
                term in item_text or term in path_str
                for term in search_terms
            )

            item.setHidden(not matches)
            if matches:
                match_count += 1

        # Update the results count label if it exists
        if hasattr(self, 'results_count_label') and self.results_count_label is not None:
            self.results_count_label.setText(f"{match_count} matches")

        # Log the number of matches
        self.log_action(f"Found {match_count} matches for search: '{search_text}'")

        # If no matches, try a more lenient search
        if match_count == 0 and len(search_terms) > 1:
            self.log_action("No matches found, trying more lenient search...")
            for i in range(self.search_results.count()):
                item = self.search_results.item(i)

                # Skip already hidden items (filtered by type)
                if item.isHidden() and current_filter != "All Types":
                    continue

                item_text = item.text().lower()
                file_path = Path(item.data(Qt.UserRole))
                path_str = str(file_path).lower()

                # Check if ANY search term matches
                matches = any(
                    term in item_text or term in path_str
                    for term in search_terms
                )

                item.setHidden(not matches)
                if matches:
                    match_count += 1

            # Update the results count label again after lenient search
            if hasattr(self, 'results_count_label') and self.results_count_label is not None:
                self.results_count_label.setText(f"{match_count} matches (lenient search)")

            self.log_action(f"Found {match_count} matches with lenient search")

        # Ensure styling is applied to visible items
        self.ensure_search_results_styling()

    def get_file_type(self, file_path):
        """Determine the type of file based on extension and location"""
        suffix = file_path.suffix.lower()
        name = file_path.name.lower()
        path_str = str(file_path).lower()

        # Check for anime (based on common patterns in anime filenames and folders)
        if ('anime' in path_str or
            any(term in path_str for term in ['sub', 'dub', 'episode']) or
            re.search(r'(\[.*?\]|\(.*?\))\s*-?\s*\d+', name)):  # Common anime naming pattern
            return "Anime"

        # Check for movies
        elif ('movies' in path_str or
            (suffix in {'.mp4', '.mkv', '.avi', '.mov', '.m4v'} and
             (re.search(r'\(\d{4}\)|\[\d{4}\]|\d{4}', name) or  # Year in parentheses or brackets
              re.search(r'720p|1080p|2160p|bluray|brrip|dvdrip', name, re.IGNORECASE)))):  # Quality indicators
            return "Movies"

        # Check for TV Shows
        elif ('tv' in path_str or
              re.search(r's\d{1,2}e\d{1,2}|season.*episode|e\d{1,2}|episode[\s._-]?\d+', name, re.IGNORECASE) or
              any(folder.lower() in ['tv shows', 'series', 'seasons'] for folder in str(file_path.parent).split(os.sep))):
            return "TV Shows"

        # Check for videos that don't match other categories
        elif suffix in {'.mp4', '.mkv', '.avi', '.mov', '.wmv', '.m4v', '.webm', '.flv'}:
            return "Videos"

        # Check for subtitles
        elif suffix in {'.srt', '.sub', '.smi', '.ssa', '.ass', '.vtt'}:
            return "Subtitles"

        # Check for images
        elif suffix in {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'}:
            return "Images"

        # Check for documents
        elif suffix in {'.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.nfo', '.md'}:
            return "Documents"

        return "Other"

    def update_search_results(self):
        """Update the search results based on current filters"""
        try:
            # Get search parameters
            query = self.search_input.text().lower()
            file_type = self.type_combo.currentText()
            size_filter = self.size_combo.currentText()
            date_filter = self.date_combo.currentText()
            sort_by = self.sort_combo.currentText()

            # Clear current results
            self.results_list.clear()

            # Apply filters
            results = []
            for file_info in self.search_files:
                # Apply search query
                if query and query not in file_info['name'].lower():
                    continue

                # Apply type filter
                if file_type != "All Types" and file_info['type'] != file_type:
                    continue

                # Apply size filter
                size_mb = file_info['size'] / (1024 * 1024)
                if size_filter == "< 100 MB" and size_mb >= 100:
                    continue
                elif size_filter == "100 MB - 1 GB" and (size_mb < 100 or size_mb >= 1024):
                    continue
                elif size_filter == "> 1 GB" and size_mb < 1024:
                    continue

                # Apply date filter
                now = datetime.now()
                file_date = file_info['modified']
                if date_filter == "Today" and file_date.date() != now.date():
                    continue
                elif date_filter == "Last 7 Days" and (now - file_date).days > 7:
                    continue
                elif date_filter == "Last 30 Days" and (now - file_date).days > 30:
                    continue
                elif date_filter == "This Year" and file_date.year != now.year:
                    continue

                results.append(file_info)

            # Sort results
            if sort_by == "Name":
                results.sort(key=lambda x: x['name'].lower())
            elif sort_by == "Date":
                results.sort(key=lambda x: x['modified'], reverse=True)
            elif sort_by == "Size":
                results.sort(key=lambda x: x['size'], reverse=True)
            elif sort_by == "Type":
                results.sort(key=lambda x: x['type'])

            # Add results to list with enhanced information
            for file_info in results:
                size_str = self.format_size(file_info['size'])
                date_str = file_info['modified'].strftime("%Y-%m-%d %H:%M")

                # Find related files (subtitles, images, etc.)
                related_files = self.find_related_files(file_info['path'])
                related_info = ""
                if related_files:
                    related_info = "\nRelated files: " + ", ".join(related_files)

                item_text = (f"{file_info['name']}\n"
                           f"Type: {file_info['type']} | Size: {size_str} | Modified: {date_str}"
                           f"{related_info}")

                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, str(file_info['path']))
                self.results_list.addItem(item)

            self.log_action(f"Found {len(results)} matching files")

        except Exception as e:
            self.log_action(f"Error updating search results: {e}")

    def find_related_files(self, file_path):
        """Find related files (subtitles, images, etc.) for a given file"""
        try:
            related = []
            path = Path(file_path)
            base_name = path.stem
            parent = path.parent

            # Look for files with the same base name but different extensions
            for related_file in parent.glob(f"{base_name}.*"):
                if related_file != path:  # Don't include the original file
                    file_type = self.get_file_type(related_file)
                    if file_type in ["Subtitles", "Images", "Documents"]:
                        related.append(f"{related_file.name} ({file_type})")

            return related
        except Exception as e:
            self.log_action(f"Error finding related files: {e}")
            return []

    def format_size(self, size_bytes):
        """Format file size in human-readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024
        return f"{size_bytes:.1f} TB"

    def open_search_result(self, item):
        """Open the selected search result"""
        try:
            file_path = Path(item.data(Qt.UserRole))
            if file_path.exists():
                # Use the default system application to open the file
                if sys.platform == 'win32':
                    os.startfile(str(file_path))
                elif sys.platform == 'darwin':
                    subprocess.run(['open', str(file_path)])
                else:
                    subprocess.run(['xdg-open', str(file_path)])
            else:
                QMessageBox.warning(self, "Error", "File not found.")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open file: {e}")

    # DRAG AND DROP EVENT HANDLING:
    # Handles files dragged into the main window
    def dragEnterEvent(self, event: QDragEnterEvent):
        logging.info("Main Window Drag Enter Event Triggered")
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event: QDropEvent):
        logging.info("Main Window Drop Event Triggered")
        try:
            file_paths = []
            for url in event.mimeData().urls():
                file_path = Path(url.toLocalFile())
                if file_path.is_file():
                    file_paths.append(str(file_path))
                    self.log_action(f"Added file for sorting: {file_path.name}")
                elif file_path.is_dir():
                    for item in file_path.iterdir():
                        if item.is_file():
                            file_paths.append(str(item))
                            self.log_action(f"Added file from folder: {item.name}")
                else:
                    self.log_action(f"Skipped unsupported item: {file_path}")

            if file_paths:
                # Add files to the file list widget
                self.file_list.add_files(file_paths)
                # Automatically trigger the sort
                self.sort_files()
        except Exception as e:
            self.log_action(f"Error handling dropped files: {e}")
            QMessageBox.critical(self, "Error", f"Failed to process dropped files: {str(e)}")

    # FILE HANDLING METHODS:
    # Methods for adding and sorting general files
    def add_files(self):
        file_paths, _ = QFileDialog.getOpenFileNames(self, "Select Files")
        if file_paths:
            self.file_list.add_files(file_paths)
            self.log_action(f"Added {len(file_paths)} files to the list")

    # MOVIE HANDLING METHODS:
    def add_movie_files(self):
        """Add movie files through file dialog with improved error handling"""
        try:
            file_paths, _ = QFileDialog.getOpenFileNames(
                self,
                "Select Movie Files",
                "",
                "Movie Files (*.mp4 *.mkv *.avi *.mov *.wmv);;All Files (*.*)"
            )

            if file_paths:
                # Process files in batches to prevent UI freezing
                batch_size = 10
                total_files = len(file_paths)
                processed = 0

                while processed < total_files:
                    batch = file_paths[processed:processed + batch_size]
                    self.movie_list.add_files(batch)
                    processed += len(batch)
                    QApplication.processEvents()  # Keep UI responsive

                self.log_action(f"Added {total_files} movie files for sorting")

        except Exception as e:
            self.log_action(f"Error adding movie files: {e}")
            QMessageBox.critical(self, "Error", f"Failed to add movie files: {e}")

    # LOGGING METHOD:
    # Adds messages to the log display and logs to file
    def log_action(self, message):
        """Log a message to the logs display and the log file"""
        # Check if logs_display is initialized
        if hasattr(self, 'logs_display') and self.logs_display is not None:
            self.logs_display.append(message)
        # Always log to the log file
        logging.info(message)

    def open_theme_settings(self):
        """Open the theme settings dialog"""
        from theme.theme_settings_dialog import ThemeSettingsDialog
        dialog = ThemeSettingsDialog(self)
        dialog.exec_()

    def configure_tmdb_api(self):
        """Configure the TMDB API key"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Configure TMDB API")
        layout = QVBoxLayout()
        dialog.setLayout(layout)

        # Add instructions
        instructions = QLabel(
            "Enter your TMDB API key. You can get one by:\n"
            "1. Creating an account at themoviedb.org\n"
            "2. Going to your account settings\n"
            "3. Clicking on the API section\n"
            "4. Requesting an API key for a developer account"
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Add input field
        api_key_input = QLineEdit()
        api_key_input.setPlaceholderText("Enter your TMDB API key")
        api_key_input.setEchoMode(QLineEdit.Password)
        if 'tmdb_api_key' in self.settings:
            api_key_input.setText(self.settings['tmdb_api_key'])
        layout.addWidget(api_key_input)

        # Add buttons
        button_box = QHBoxLayout()
        ok_button = QPushButton("OK")
        cancel_button = QPushButton("Cancel")
        button_box.addWidget(ok_button)
        button_box.addWidget(cancel_button)
        layout.addLayout(button_box)

        # Connect buttons
        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)

        if dialog.exec_() == QDialog.Accepted:
            api_key = api_key_input.text().strip()
            if api_key:
                # Save API key to settings
                self.settings['tmdb_api_key'] = api_key
                self.save_settings()

                # Initialize TMDB helper with new key
                if hasattr(self, 'tmdb'):
                    self.tmdb.api_key = api_key

                QMessageBox.information(self, "Success", "TMDB API key has been configured.")
            else:
                QMessageBox.warning(self, "Error", "Please enter a valid API key.")

# APPLICATION ENTRY POINT:
# Creates and runs the application when the script is executed directly
if __name__ == "__main__":
    app = QApplication(sys.argv)
    main_window = MainWindow()
    main_window.show()
    sys.exit(app.exec_())