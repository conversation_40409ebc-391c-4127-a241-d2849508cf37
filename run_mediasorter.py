#!/usr/bin/env python3
"""
MediaSorter Cross-Platform Launcher
This script automatically finds and runs MediaSorter regardless of folder location or OS.
"""

import os
import sys
import subprocess
from pathlib import Path

def find_brain_folder():
    """Find the Brain folder starting from the script location"""
    script_dir = Path(__file__).resolve().parent
    
    # Check if Brain folder exists in current directory
    brain_folder = script_dir / "0.3 Brain Folder"
    if brain_folder.exists() and brain_folder.is_dir():
        return brain_folder
    
    # Search in subdirectories
    for item in script_dir.iterdir():
        if item.is_dir():
            potential_brain = item / "0.3 Brain Folder"
            if potential_brain.exists() and potential_brain.is_dir():
                return potential_brain
    
    return None

def main():
    print("MediaSorter Cross-Platform Launcher")
    print("=" * 35)
    
    # Find the Brain folder
    brain_folder = find_brain_folder()
    
    if not brain_folder:
        print("[ERROR] MediaSorter Brain folder not found!")
        print("Please ensure this launcher is in the same directory as '0.3 Brain Folder'")
        print(f"Current directory: {Path(__file__).resolve().parent}")
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Find the main Python file
    main_file = brain_folder / "MainCodeFile.py"
    if not main_file.exists():
        print(f"[ERROR] MainCodeFile.py not found in {brain_folder}")
        input("Press Enter to exit...")
        sys.exit(1)
    
    print(f"Found MediaSorter in: {brain_folder.parent}")
    print("Starting MediaSorter...")
    print()
    
    # Change to the Brain folder directory
    os.chdir(brain_folder)
    
    # Run the main Python file
    try:
        subprocess.run([sys.executable, "MainCodeFile.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] MediaSorter failed to start: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nMediaSorter was interrupted by user")
    except Exception as e:
        print(f"[ERROR] Unexpected error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
