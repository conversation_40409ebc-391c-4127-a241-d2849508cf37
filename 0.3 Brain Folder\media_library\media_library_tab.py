"""
MediaSorter Media Library Tab
Provides a Netflix/Plex-like interface for browsing and playing media
"""

from pathlib import Path
import json
import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QScrollArea, QGridLayout, QDialog, QMessageBox, QFrame, QButtonGroup, QSplitter
)
from PyQt5.QtCore import Qt, QSize, QTimer
from PyQt5.QtGui import QPixmap, QCursor, QFont, QIcon
import subprocess
import os
from movie_code.movie_handler import get_movies_folder
from .movie_details_dialog import MovieDetailsDialog

class MediaLibraryTab(QWidget):
    """Main class for the Media Library tab"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.movies_folder = Path(self.parent.settings.get('movies_folder', ''))
        self.current_view = 'all'  # Can be 'all', 'recent', or 'genres'
        self.init_ui()
        
    def init_ui(self):
        """Initialize the UI components"""
        # Main layout
        layout = QHBoxLayout()
        self.setLayout(layout)
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # Left sidebar
        sidebar = QFrame()
        sidebar.setMaximumWidth(250)
        sidebar.setMinimumWidth(200)
        sidebar_layout = QVBoxLayout()
        sidebar.setLayout(sidebar_layout)
        
        # Add navigation buttons
        self.create_nav_button("Movies", "🎬", sidebar_layout)
        self.create_nav_button("Recently Added", "🆕", sidebar_layout)
        self.create_nav_button("Favorites", "⭐", sidebar_layout)
        self.create_nav_button("Playlists", "📑", sidebar_layout)
        
        # Add stretch to push buttons to top
        sidebar_layout.addStretch()
        
        # Main content area
        content = QFrame()
        content_layout = QVBoxLayout()
        content.setLayout(content_layout)
        
        # Header with title and controls
        header = QHBoxLayout()
        title = QLabel("Movie Library")
        title.setStyleSheet("font-size: 24px; font-weight: bold;")
        header.addWidget(title)
        
        # Add sort/filter controls
        sort_btn = QPushButton("Sort By ▼")
        sort_btn.setStyleSheet(self.get_button_style())
        header.addWidget(sort_btn)
        
        filter_btn = QPushButton("Filter ▼")
        filter_btn.setStyleSheet(self.get_button_style())
        header.addWidget(filter_btn)
        
        header.addStretch()
        content_layout.addLayout(header)
        
        # Grid view for movies
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        grid_container = QWidget()
        self.grid_layout = QVBoxLayout()
        grid_container.setLayout(self.grid_layout)
        
        # Add some placeholder content
        placeholder = QLabel("No movies found. Add movies to your library to see them here.")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet("color: #666; padding: 20px;")
        self.grid_layout.addWidget(placeholder)
        
        scroll.setWidget(grid_container)
        content_layout.addWidget(scroll)
        
        # Add components to splitter
        splitter.addWidget(sidebar)
        splitter.addWidget(content)
        
        # Set initial splitter sizes
        splitter.setSizes([250, 750])
        
        # Apply styles
        self.setStyleSheet("""
            QFrame {
                background: #1E1E1E;
                border: none;
            }
            QLabel {
                color: white;
            }
            QScrollArea {
                border: none;
            }
        """)
        
    def create_nav_button(self, text, icon, layout):
        """Create a navigation button with icon and text"""
        btn = QPushButton(f" {icon} {text}")
        btn.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 10px;
                border: none;
                border-radius: 5px;
                color: white;
                font-size: 14px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        """)
        layout.addWidget(btn)
        
    def get_button_style(self):
        """Get the common button style"""
        return """
            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                color: white;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.2);
            }
        """
    
    def create_movie_widget(self, movie_folder):
        """Create a widget for a movie"""
        widget = QFrame()
        widget.setFixedSize(200, 300)
        widget.setFrameStyle(QFrame.NoFrame)
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        try:
            # Load movie info
            info_file = movie_folder / f"{movie_folder.name}_info" / "movie_info.json"
            if not info_file.exists():
                return None
                
            with open(info_file, 'r', encoding='utf-8') as f:
                movie_info = json.load(f)
            
            # Poster
            poster_path = movie_folder / f"{movie_folder.name}_info" / "posters" / "main_poster.jpg"
            if poster_path.exists():
                poster_label = QLabel()
                pixmap = QPixmap(str(poster_path))
                scaled_pixmap = pixmap.scaled(180, 270, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                poster_label.setPixmap(scaled_pixmap)
                layout.addWidget(poster_label, alignment=Qt.AlignCenter)
            
            # Title
            title = QLabel(movie_info.get('title', 'Unknown Title'))
            title.setWordWrap(True)
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("font-weight: bold;")
            layout.addWidget(title)
            
            # Make widget clickable
            widget.mousePressEvent = lambda e: self.show_movie_details(movie_folder, movie_info)
            widget.setCursor(Qt.PointingHandCursor)
            widget.setStyleSheet("""
                QFrame {
                    background-color: #2d2d2d;
                    border-radius: 8px;
                }
                QFrame:hover {
                    background-color: #3d3d3d;
                }
            """)
            
            return widget
        except Exception as e:
            print(f"Error creating movie widget: {str(e)}")
            return None
    
    def show_movie_details(self, movie_folder, movie_info):
        """Show detailed information about a movie"""
        dialog = MovieDetailsDialog(self.parent, movie_folder, movie_info)
        dialog.exec_()
    
    def switch_movie_view(self, view_type):
        """Switch between different movie views"""
        self.current_view = view_type
        if view_type == 'all':
            self.load_all_movies()
        elif view_type == 'recent':
            self.load_recent_movies()
        elif view_type == 'genres':
            self.load_movies_by_genre()
    
    def clear_grid(self):
        """Clear all items from the grid layout"""
        while self.grid_layout.count():
            item = self.grid_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
    
    def load_all_movies(self):
        """Load all movies into the grid"""
        self.clear_grid()
        
        if not self.movies_folder.exists():
            QMessageBox.warning(self, "Error", "Movies folder not found!")
            return
            
        row = 0
        col = 0
        max_cols = 4
        
        for movie_folder in self.movies_folder.iterdir():
            if not movie_folder.is_dir():
                continue
                
            movie_widget = self.create_movie_widget(movie_folder)
            if movie_widget:
                self.grid_layout.addWidget(movie_widget, row, col)
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1
    
    def load_recent_movies(self):
        """Load recently added movies"""
        self.clear_grid()
        
        if not self.movies_folder.exists():
            QMessageBox.warning(self, "Error", "Movies folder not found!")
            return
            
        # Get all movie folders and sort by modification time
        movie_folders = [(f, f.stat().st_mtime) for f in self.movies_folder.iterdir() if f.is_dir()]
        movie_folders.sort(key=lambda x: x[1], reverse=True)
        
        row = 0
        col = 0
        max_cols = 4
        
        for movie_folder, _ in movie_folders[:20]:  # Show only the 20 most recent
            movie_widget = self.create_movie_widget(movie_folder)
            if movie_widget:
                self.grid_layout.addWidget(movie_widget, row, col)
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1
    
    def load_movies_by_genre(self):
        """Load movies organized by genre"""
        self.clear_grid()
        
        if not self.movies_folder.exists():
            QMessageBox.warning(self, "Error", "Movies folder not found!")
            return
            
        # Collect movies by genre
        genres = {}
        for movie_folder in self.movies_folder.iterdir():
            if not movie_folder.is_dir():
                continue
                
            info_file = movie_folder / f"{movie_folder.name}_info" / "movie_info.json"
            if not info_file.exists():
                continue
                
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    movie_info = json.load(f)
                    
                movie_genres = movie_info.get('genres', [])
                for genre in movie_genres:
                    if genre not in genres:
                        genres[genre] = []
                    genres[genre].append(movie_folder)
            except Exception as e:
                print(f"Error loading movie info: {str(e)}")
        
        # Display movies by genre
        row = 0
        for genre, movies in sorted(genres.items()):
            # Add genre header
            header = QLabel(genre)
            header.setFont(QFont('Arial', 14, QFont.Bold))
            self.grid_layout.addWidget(header, row, 0, 1, 4)
            row += 1
            
            # Add movies in this genre
            col = 0
            max_cols = 4
            
            for movie_folder in movies:
                movie_widget = self.create_movie_widget(movie_folder)
                if movie_widget:
                    self.grid_layout.addWidget(movie_widget, row, col)
                    col += 1
                    if col >= max_cols:
                        col = 0
                        row += 1
            
            row += 1  # Add space between genres
    
    def search_movies(self):
        """Search for movies by title"""
        query = self.search_input.text().lower()
        self.clear_grid()
        
        if not query:
            self.switch_movie_view(self.current_view)
            return
            
        if not self.movies_folder.exists():
            QMessageBox.warning(self, "Error", "Movies folder not found!")
            return
            
        row = 0
        col = 0
        max_cols = 4
        
        for movie_folder in self.movies_folder.iterdir():
            if not movie_folder.is_dir():
                continue
                
            info_file = movie_folder / f"{movie_folder.name}_info" / "movie_info.json"
            if not info_file.exists():
                continue
                
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    movie_info = json.load(f)
                    
                title = movie_info.get('title', '').lower()
                overview = movie_info.get('overview', '').lower()
                
                if query in title or query in overview:
                    movie_widget = self.create_movie_widget(movie_folder)
                    if movie_widget:
                        self.grid_layout.addWidget(movie_widget, row, col)
                        col += 1
                        if col >= max_cols:
                            col = 0
                            row += 1
            except Exception as e:
                print(f"Error searching movie: {str(e)}") 