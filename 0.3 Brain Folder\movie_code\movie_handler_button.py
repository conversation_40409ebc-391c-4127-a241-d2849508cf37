# movie_handler_button.py v2.3

from PyQt5.QtWidgets import QPushButton, QMessageBox
from pathlib import Path
import logging
import os
import shutil
from datetime import datetime
from settings.settings_toggle_switch import get_season_folder_path, use_season_folder

def find_mediasorter_root():
    """
    Find the MediaSorter root directory by looking for the characteristic folder structure.
    This makes the application portable regardless of the main folder name or location.
    """
    current = Path(__file__).resolve().parent.parent  # Start from the Brain folder

    # Check if we're already in the Brain folder (0.3 Brain Folder)
    if current.name == "0.3 Brain Folder":
        return current.parent

    # Look for the Brain folder in the current directory or parent directories
    while current.parent != current:  # Stop at filesystem root
        # Check if this directory contains the Brain folder
        brain_folder = current / "0.3 Brain Folder"
        if brain_folder.exists() and brain_folder.is_dir():
            return current
        current = current.parent

    # If we can't find the characteristic structure, use the parent of the current file's directory
    # This ensures the app works even if the folder structure is incomplete
    return Path(__file__).resolve().parent.parent.parent

# Configure logging
main_folder = find_mediasorter_root()
log_dir = main_folder / '0.3 Brain Folder' / 'logs'
log_dir.mkdir(parents=True, exist_ok=True)

# Create a logger specific to this module
movie_button_logger = logging.getLogger('movie_handler_button')
movie_button_logger.setLevel(logging.INFO)

# Remove any existing handlers to avoid duplicates
for handler in movie_button_logger.handlers[:]:
    movie_button_logger.removeHandler(handler)

# Create file handler
file_handler = logging.FileHandler(str(log_dir / 'movie_handler_log.txt'))
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
movie_button_logger.addHandler(file_handler)

class MovieSorterButton(QPushButton):
    def __init__(self, parent=None):
        super().__init__("Sort Movies", parent)
        self.clicked.connect(self.sort_movies)
        self.main_window = None
        self.setup_logging()

    def setup_logging(self):
        """Set up logging for this button instance"""
        # We're already using the module-level logger, so no need to configure again
        movie_button_logger.info("MovieSorterButton initialized")

    def set_main_window(self, main_window):
        self.main_window = main_window

    def sort_movies(self):
        if not self.main_window or not hasattr(self.main_window, 'movie_list'):
            QMessageBox.warning(self, "Error", "Movie list not initialized")
            return

        movie_list = self.main_window.movie_list
        if not movie_list.file_paths:
            QMessageBox.information(self, "No Movies", "No movies to sort")
            return

        try:
            self.process_gui_movies(movie_list.file_paths)
            movie_list.clear()
            QMessageBox.information(self, "Success", "Movies sorted successfully")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error sorting movies: {str(e)}")
            movie_button_logger.error(f"Error in sort_movies: {e}")

    def process_gui_movies(self, movie_paths):
        """Process movies from the GUI list widget"""
        if not movie_paths:
            return

        sorted_count = 0
        skipped_count = 0
        error_count = 0

        for movie_path in movie_paths:
            try:
                path = Path(movie_path)
                if not path.exists():
                    if self.main_window:
                        self.main_window.log_action(f"Movie file not found: {path}")
                    movie_button_logger.warning(f"Movie file not found: {path}")
                    skipped_count += 1
                    continue

                # Check if it's a valid movie file
                if not self.is_valid_movie_file(path):
                    if self.main_window:
                        self.main_window.log_action(f"Invalid movie file: {path.name}")
                    movie_button_logger.info(f"Invalid movie file: {path.name}")
                    skipped_count += 1
                    continue

                # Get the destination folder based on movie name pattern
                dest_folder = self.get_movie_destination_folder(path)
                if not dest_folder:
                    if self.main_window:
                        self.main_window.log_action(f"Could not determine destination for: {path.name}")
                    movie_button_logger.info(f"Could not determine destination for: {path.name}")
                    skipped_count += 1
                    continue

                # Create destination folder if it doesn't exist
                dest_folder.mkdir(parents=True, exist_ok=True)

                # Move the movie file
                dest_path = dest_folder / path.name
                if dest_path.exists():
                    new_name = self.generate_unique_name(dest_path)
                    dest_path = dest_folder / new_name

                shutil.move(str(path), str(dest_path))
                sorted_count += 1

                if self.main_window:
                    self.main_window.log_action(f"Moved {path.name} to {dest_folder}")
                movie_button_logger.info(f"Moved {path.name} to {dest_folder}")

            except Exception as e:
                error_count += 1
                if self.main_window:
                    self.main_window.log_action(f"Error processing {path.name}: {e}")
                movie_button_logger.error(f"Error processing {movie_path}: {e}")

        # Log summary
        summary = f"Sorted: {sorted_count}, Skipped: {skipped_count}, Errors: {error_count}"
        if self.main_window:
            self.main_window.log_action(f"Movie sort summary - {summary}")
        movie_button_logger.info(f"Movie sort summary - {summary}")

    def is_valid_movie_file(self, file_path):
        """Check if the file is a valid movie file"""
        valid_extensions = {'.mp4', '.mkv', '.avi', '.mov', '.wmv'}
        return file_path.suffix.lower() in valid_extensions

    def get_movie_destination_folder(self, file_path):
        """Determine the destination folder based on movie name pattern"""
        # Base movies directory
        movies_dir = Path("sorted/movies")

        # Extract movie name and potential season info
        name = file_path.stem
        season_match = None  # You can implement season detection logic here if needed

        if use_season_folder() and season_match:
            # If season folder setting is enabled and season is detected
            season_folder = get_season_folder_path()
            if season_folder:
                return Path(season_folder)

        # Default to movies directory
        return movies_dir

    def generate_unique_name(self, file_path):
        """Generate a unique filename by adding timestamp"""
        stem = file_path.stem
        suffix = file_path.suffix
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{stem}_{timestamp}{suffix}"