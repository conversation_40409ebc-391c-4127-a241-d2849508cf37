2025-04-30 21:42:35,564 - INFO - Folders verified.
2025-04-30 21:42:35,618 - INFO - MediaSorter application starting - Log test
2025-04-30 21:42:35,964 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:42:35,965 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:42:35,966 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:42:35,970 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:42:35,973 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:42:35,977 - INFO - Found 40 files in search
2025-04-30 21:42:35,994 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:42:35,995 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:42:36,077 - INFO - Application started
2025-04-30 21:50:28,130 - INFO - Folders verified.
2025-04-30 21:50:28,155 - INFO - MediaSorter application starting - Log test
2025-04-30 21:51:01,217 - INFO - Folders verified.
2025-04-30 21:51:01,240 - INFO - MediaSorter application starting - Log test
2025-04-30 21:51:01,466 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:51:01,612 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:51:01,613 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:51:01,615 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:51:01,622 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:51:01,626 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:51:01,630 - INFO - Found 41 files in search
2025-04-30 21:51:01,670 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:51:01,670 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:51:01,793 - INFO - Application started
2025-04-30 21:51:28,987 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:09,879 - INFO - Folders verified.
2025-04-30 21:52:09,895 - INFO - MediaSorter application starting - Log test
2025-04-30 21:52:10,124 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:10,243 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:10,244 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:10,245 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:10,250 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:52:10,254 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:10,257 - INFO - Found 41 files in search
2025-04-30 21:52:10,278 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:10,278 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:10,352 - INFO - Application started
2025-04-30 21:52:18,463 - INFO - Added movie file: Havoc (2025).mp4
2025-04-30 21:52:18,464 - INFO - Added movie file: The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4
2025-04-30 21:52:18,464 - INFO - Added movie file: A Knights War (2025).mp4
2025-04-30 21:52:18,464 - INFO - Successfully added 3 movie files
2025-04-30 21:52:19,799 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:19,800 - INFO - Using movie destination folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:19,800 - INFO - Enabled movie destinations: Movies (C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies)
2025-04-30 21:52:19,800 - INFO - Starting movie processing for 3 files...
2025-04-30 21:52:19,800 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\Havoc (2025).mp4
2025-04-30 21:52:19,800 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:19,918 - INFO - Successfully sorted movie: Havoc (2025).mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\Havoc (2025)\Havoc (2025).mp4
2025-04-30 21:52:19,919 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4
2025-04-30 21:52:19,919 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:19,981 - INFO - Successfully sorted movie: The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\The Monkey (2025)\The Monkey (2025).mp4
2025-04-30 21:52:19,982 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\A Knights War (2025).mp4
2025-04-30 21:52:19,983 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:20,056 - INFO - Successfully sorted movie: A Knights War (2025).mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\A Knights War (2025)\A Knights War (2025).mp4
2025-04-30 21:52:20,057 - INFO - Movie processing complete. Total: 3, Sorted: 3, Unsorted: 0
2025-04-30 21:52:28,583 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:47,296 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:47,297 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:47,297 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:47,305 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:52:47,308 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:47,311 - INFO - Found 6 files in search
2025-04-30 21:52:50,820 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:50,820 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:50,820 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:50,835 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:52:50,838 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:50,840 - INFO - Found 6 files in search
2025-04-30 21:52:51,450 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:51,451 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:51,451 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:51,457 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:52:51,460 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:51,462 - INFO - Found 6 files in search
2025-04-30 21:52:53,444 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:53,444 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:53,444 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:53,451 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:52:53,453 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:53,456 - INFO - Found 4 files in search
2025-04-30 21:52:54,468 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:54,468 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:54,469 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:54,475 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:52:54,478 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:54,481 - INFO - Found 4 files in search
2025-04-30 21:52:54,951 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:54,951 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:54,952 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:54,958 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:52:54,961 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:54,964 - INFO - Found 4 files in search
2025-04-30 21:52:55,262 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:52:55,263 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:55,263 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:52:55,270 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:52:55,273 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:52:55,277 - INFO - Found 4 files in search
2025-04-30 21:53:30,533 - INFO - Fetched information for: A Knights War (2025).mp4
2025-04-30 21:53:36,185 - INFO - Fetched information for: Havoc (2025).mp4
2025-04-30 21:53:41,894 - INFO - Fetched information for: The Monkey (2025).mp4
2025-04-30 21:53:44,244 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:53:44,244 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:53:44,244 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:53:44,253 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:53:44,257 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:53:44,268 - INFO - Found 8 files in search
2025-04-30 21:53:45,228 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:53:45,229 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:53:45,229 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:53:45,238 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:53:45,243 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:53:45,251 - INFO - Found 8 files in search
2025-04-30 21:53:45,787 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:53:45,788 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:53:45,788 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:53:45,798 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:53:45,803 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:53:45,806 - INFO - Found 8 files in search
2025-04-30 21:53:46,293 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:53:46,293 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:53:46,294 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 21:53:46,302 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:53:46,307 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:53:46,310 - INFO - Found 8 files in search
2025-04-30 21:53:51,909 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:53:56,525 - INFO - Using enabled movie destination: Movies at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 21:55:39,096 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 21:58:44,550 - INFO - Folders verified.
2025-04-30 21:58:44,566 - INFO - MediaSorter application starting - Log test
2025-04-30 21:58:44,806 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 21:58:44,958 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 21:58:44,959 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:58:44,961 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 21:58:44,969 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:58:44,976 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:58:44,978 - INFO - Found 49 files in search
2025-04-30 21:58:45,025 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 21:58:45,026 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 21:58:45,145 - INFO - Application started
2025-04-30 21:58:58,108 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 21:58:58,109 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:58:58,109 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 21:58:58,115 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 21:58:58,121 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 21:58:58,122 - INFO - Found 49 files in search
2025-04-30 21:59:12,885 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 22:04:07,398 - INFO - Folders verified.
2025-04-30 22:04:07,421 - INFO - MediaSorter application starting - Log test
2025-04-30 22:04:07,657 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 22:04:07,804 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 22:04:07,805 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 22:04:07,806 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 22:04:07,814 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 22:04:07,821 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 22:04:07,822 - INFO - Found 49 files in search
2025-04-30 22:04:07,870 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 22:04:07,871 - INFO - Using default movies folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\04. Movies & TV Show\movies
2025-04-30 22:04:07,967 - INFO - Application started
2025-04-30 22:04:44,040 - INFO - Added movie file: A Knights War (2025).mp4
2025-04-30 22:04:44,040 - INFO - Added movie file: Havoc (2025).mp4
2025-04-30 22:04:44,040 - INFO - Added movie file: The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4
2025-04-30 22:04:44,040 - INFO - Successfully added 3 movie files
2025-04-30 22:04:44,983 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:04:44,983 - INFO - Using movie destination folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:04:44,984 - INFO - Enabled movie destinations: New Movie Destination (C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies)
2025-04-30 22:04:44,984 - INFO - Starting movie processing for 3 files...
2025-04-30 22:04:44,984 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\A Knights War (2025).mp4
2025-04-30 22:04:44,984 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:04:44,985 - WARNING - Movie already exists: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\A Knights War (2025)\A Knights War (2025).mp4
2025-04-30 22:04:44,985 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\Havoc (2025).mp4
2025-04-30 22:04:44,985 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:04:44,986 - WARNING - Movie already exists: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\Havoc (2025)\Havoc (2025).mp4
2025-04-30 22:04:44,986 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4
2025-04-30 22:04:44,986 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:04:44,987 - WARNING - Movie already exists: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\The Monkey (2025)\The Monkey (2025).mp4
2025-04-30 22:04:44,987 - INFO - Movie processing complete. Total: 3, Sorted: 0, Unsorted: 3
2025-04-30 22:05:58,495 - INFO - Folders verified.
2025-04-30 22:05:58,511 - INFO - MediaSorter application starting - Log test
2025-04-30 22:05:58,735 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:05:58,853 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:05:58,853 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 22:05:58,854 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:05:58,858 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 22:05:58,862 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 22:05:58,866 - INFO - Found 41 files in search
2025-04-30 22:05:58,883 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:05:58,883 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:05:58,955 - INFO - Application started
2025-04-30 22:06:07,611 - INFO - Added movie file: A Knights War (2025).mp4
2025-04-30 22:06:07,611 - INFO - Added movie file: Havoc (2025).mp4
2025-04-30 22:06:07,612 - INFO - Added movie file: The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4
2025-04-30 22:06:07,612 - INFO - Successfully added 3 movie files
2025-04-30 22:06:08,731 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:06:08,731 - INFO - Using movie destination folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:06:08,731 - INFO - Enabled movie destinations: New Movie Destination (C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies)
2025-04-30 22:06:08,731 - INFO - Starting movie processing for 3 files...
2025-04-30 22:06:08,731 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\A Knights War (2025).mp4
2025-04-30 22:06:08,731 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:06:08,810 - INFO - Successfully sorted movie: A Knights War (2025).mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\A Knights War (2025)\A Knights War (2025).mp4
2025-04-30 22:06:08,811 - INFO - Created metadata file: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\A Knights War (2025)\metadata\movie_info.json
2025-04-30 22:06:08,811 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\Havoc (2025).mp4
2025-04-30 22:06:08,812 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:06:08,931 - INFO - Successfully sorted movie: Havoc (2025).mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\Havoc (2025)\Havoc (2025).mp4
2025-04-30 22:06:08,932 - INFO - Created metadata file: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\Havoc (2025)\metadata\movie_info.json
2025-04-30 22:06:08,933 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4
2025-04-30 22:06:08,933 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:06:08,997 - INFO - Successfully sorted movie: The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\The Monkey (2025)\The Monkey (2025).mp4
2025-04-30 22:06:08,998 - INFO - Created metadata file: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\The Monkey (2025)\metadata\movie_info.json
2025-04-30 22:06:08,998 - INFO - Movie processing complete. Total: 3, Sorted: 3, Unsorted: 0
2025-04-30 22:07:25,163 - INFO - Folders verified.
2025-04-30 22:07:25,177 - INFO - MediaSorter application starting - Log test
2025-04-30 22:07:25,394 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:07:25,521 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:07:25,522 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 22:07:25,523 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:07:25,528 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 22:07:25,532 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 22:07:25,535 - INFO - Found 45 files in search
2025-04-30 22:07:25,554 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:07:25,555 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:07:25,637 - INFO - Application started
2025-04-30 22:09:29,190 - INFO - Folders verified.
2025-04-30 22:09:29,214 - INFO - MediaSorter application starting - Log test
2025-04-30 22:09:29,445 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:09:29,607 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:09:29,608 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 22:09:29,609 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:09:29,614 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter
2025-04-30 22:09:29,618 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-04-30 22:09:29,623 - INFO - Found 45 files in search
2025-04-30 22:09:29,667 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:09:29,668 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:09:29,767 - INFO - Application started
2025-04-30 22:09:51,668 - INFO - Added movie file: A Knights War (2025).mp4
2025-04-30 22:09:51,668 - INFO - Added movie file: Havoc (2025).mp4
2025-04-30 22:09:51,668 - INFO - Added movie file: The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4
2025-04-30 22:09:51,668 - INFO - Successfully added 3 movie files
2025-04-30 22:09:52,806 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-04-30 22:09:52,806 - INFO - Using movie destination folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:09:52,806 - INFO - Enabled movie destinations: New Movie Destination (C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies)
2025-04-30 22:09:52,806 - INFO - Starting movie processing for 3 files...
2025-04-30 22:09:52,807 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\A Knights War (2025).mp4
2025-04-30 22:09:52,807 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:09:52,890 - INFO - Successfully sorted movie: A Knights War (2025).mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\A Knights War (2025)\A Knights War (2025).mp4
2025-04-30 22:09:52,890 - INFO - Metadata file creation is disabled
2025-04-30 22:09:52,890 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\Havoc (2025).mp4
2025-04-30 22:09:52,890 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:09:53,011 - INFO - Successfully sorted movie: Havoc (2025).mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\Havoc (2025)\Havoc (2025).mp4
2025-04-30 22:09:53,011 - INFO - Metadata file creation is disabled
2025-04-30 22:09:53,012 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4
2025-04-30 22:09:53,012 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-04-30 22:09:53,078 - INFO - Successfully sorted movie: The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies\The Monkey (2025)\The Monkey (2025).mp4
2025-04-30 22:09:53,078 - INFO - Metadata file creation is disabled
2025-04-30 22:09:53,078 - INFO - Movie processing complete. Total: 3, Sorted: 3, Unsorted: 0
2025-05-25 14:23:11,959 - INFO - Folders verified.
2025-05-25 14:23:11,981 - INFO - MediaSorter application starting - Log test
2025-05-25 14:23:12,206 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:23:12,325 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:23:12,326 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-05-25 14:23:12,327 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:23:12,331 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:23:12,335 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows
2025-05-25 14:23:12,338 - INFO - Found 41 files in search
2025-05-25 14:23:12,355 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:23:12,355 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:23:12,439 - INFO - Application started
2025-05-25 14:23:49,730 - INFO - Added TV show file: Ubel Blatt Episode 3 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,730 - INFO - Added TV show file: Ubel Blatt Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,730 - INFO - Added TV show file: Ubel Blatt Episode 5 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,731 - INFO - Added TV show file: Ubel Blatt Episode 6 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,731 - INFO - Added TV show file: Ubel Blatt Episode 7 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,731 - INFO - Added TV show file: Ubel Blatt Episode 8 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,731 - INFO - Added TV show file: Ubel Blatt Episode 9 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,731 - INFO - Added TV show file: Ubel Blatt Episode 10 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,731 - INFO - Added TV show file: Ubel Blatt Episode 11 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,731 - INFO - Added TV show file: Ubel Blatt Episode 12 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,731 - INFO - Added TV show file: Witch Watch Episode 1 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,731 - INFO - Added TV show file: Beheneko- The Elf-Girl's Cat is Secretly an S-Ranked Monster! Episode 1 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free_2.mp4
2025-05-25 14:23:49,732 - INFO - Added TV show file: Beheneko- The Elf-Girl's Cat is Secretly an S-Ranked Monster! Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,732 - INFO - Added TV show file: From Old Country Bumpkin to Master Swordsman Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,732 - INFO - Added TV show file: I Left My A-Rank Party to Help My Former Students Reach the Dungeon Depths! Episode 13 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,732 - INFO - Added TV show file: I've Been Killing Slimes for 300 Years and Maxed Out My Level Season 2 Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,732 - INFO - Added TV show file: Lazarus Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,732 - INFO - Added TV show file: The Brilliant Healer's New Life in the Shadows Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,732 - INFO - Added TV show file: The Gorilla God's Go-To Girl Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,732 - INFO - Added TV show file: The Unaware Atelier Meister Episode 5 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,732 - INFO - Added TV show file: To Be Hero X Episode 3 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,733 - INFO - Added TV show file: To Be Hero X Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,733 - INFO - Added TV show file: Ubel Blatt Episode 1 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,733 - INFO - Added TV show file: Ubel Blatt Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:49,733 - INFO - Successfully added 24 TV show files
2025-05-25 14:23:54,417 - INFO - Processing file: Ubel Blatt Episode 3 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:54,520 - INFO - Moved Ubel Blatt Episode 3 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E03.mp4
2025-05-25 14:23:54,520 - INFO - Processing file: Ubel Blatt Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:54,622 - INFO - Moved Ubel Blatt Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E04.mp4
2025-05-25 14:23:54,622 - INFO - Processing file: Ubel Blatt Episode 5 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:54,724 - INFO - Moved Ubel Blatt Episode 5 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E05.mp4
2025-05-25 14:23:54,724 - INFO - Processing file: Ubel Blatt Episode 6 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:54,826 - INFO - Moved Ubel Blatt Episode 6 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E06.mp4
2025-05-25 14:23:54,826 - INFO - Processing file: Ubel Blatt Episode 7 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:54,928 - INFO - Moved Ubel Blatt Episode 7 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E07.mp4
2025-05-25 14:23:54,928 - INFO - Processing file: Ubel Blatt Episode 8 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:55,030 - INFO - Moved Ubel Blatt Episode 8 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E08.mp4
2025-05-25 14:23:55,030 - INFO - Processing file: Ubel Blatt Episode 9 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:55,131 - INFO - Moved Ubel Blatt Episode 9 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E09.mp4
2025-05-25 14:23:55,132 - INFO - Processing file: Ubel Blatt Episode 10 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:55,233 - INFO - Moved Ubel Blatt Episode 10 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E10.mp4
2025-05-25 14:23:55,233 - INFO - Processing file: Ubel Blatt Episode 11 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:55,335 - INFO - Moved Ubel Blatt Episode 11 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E11.mp4
2025-05-25 14:23:55,335 - INFO - Processing file: Ubel Blatt Episode 12 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:55,437 - INFO - Moved Ubel Blatt Episode 12 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E12.mp4
2025-05-25 14:23:55,437 - INFO - Processing file: Witch Watch Episode 1 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:55,539 - INFO - Moved Witch Watch Episode 1 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Witch Watch\Season 01\Witch Watch - S01E01.mp4
2025-05-25 14:23:55,539 - INFO - Processing file: Beheneko- The Elf-Girl's Cat is Secretly an S-Ranked Monster! Episode 1 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free_2.mp4
2025-05-25 14:23:55,641 - INFO - Moved Beheneko- The Elf-Girl's Cat is Secretly an S-Ranked Monster! Episode 1 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free_2.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Beheneko  The Elf Girl's Cat is Secretly an S Ranked Monster!\Season 01\Beheneko  The Elf Girl's Cat is Secretly an S Ranked Monster! - S01E01.mp4
2025-05-25 14:23:55,641 - INFO - Processing file: Beheneko- The Elf-Girl's Cat is Secretly an S-Ranked Monster! Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:55,743 - INFO - Moved Beheneko- The Elf-Girl's Cat is Secretly an S-Ranked Monster! Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Beheneko  The Elf Girl's Cat is Secretly an S Ranked Monster!\Season 01\Beheneko  The Elf Girl's Cat is Secretly an S Ranked Monster! - S01E02.mp4
2025-05-25 14:23:55,743 - INFO - Processing file: From Old Country Bumpkin to Master Swordsman Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:55,845 - INFO - Moved From Old Country Bumpkin to Master Swordsman Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\From Old Country Bumpkin to Master Swordsman\Season 01\From Old Country Bumpkin to Master Swordsman - S01E04.mp4
2025-05-25 14:23:55,845 - INFO - Processing file: I Left My A-Rank Party to Help My Former Students Reach the Dungeon Depths! Episode 13 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:55,947 - INFO - Moved I Left My A-Rank Party to Help My Former Students Reach the Dungeon Depths! Episode 13 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\I Left My A Rank Party to Help My Former Students Reach the Dungeon Depths!\Season 01\I Left My A Rank Party to Help My Former Students Reach the Dungeon Depths! - S01E13.mp4
2025-05-25 14:23:55,947 - INFO - Processing file: I've Been Killing Slimes for 300 Years and Maxed Out My Level Season 2 Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:56,049 - INFO - Moved I've Been Killing Slimes for 300 Years and Maxed Out My Level Season 2 Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\I've Been Killing Slimes for 300 Years and Maxed Out My Level\Season 02\I've Been Killing Slimes for 300 Years and Maxed Out My Level - S02E02.mp4
2025-05-25 14:23:56,050 - INFO - Processing file: Lazarus Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:56,152 - INFO - Moved Lazarus Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Lazarus\Season 01\Lazarus - S01E04.mp4
2025-05-25 14:23:56,152 - INFO - Processing file: The Brilliant Healer's New Life in the Shadows Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:56,254 - INFO - Moved The Brilliant Healer's New Life in the Shadows Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\The Brilliant Healer's New Life in the Shadows\Season 01\The Brilliant Healer's New Life in the Shadows - S01E02.mp4
2025-05-25 14:23:56,254 - INFO - Processing file: The Gorilla God's Go-To Girl Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:56,356 - INFO - Moved The Gorilla God's Go-To Girl Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\The Gorilla God's Go To Girl\Season 01\The Gorilla God's Go To Girl - S01E02.mp4
2025-05-25 14:23:56,356 - INFO - Processing file: The Unaware Atelier Meister Episode 5 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:56,458 - INFO - Moved The Unaware Atelier Meister Episode 5 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\The Unaware Atelier Meister\Season 01\The Unaware Atelier Meister - S01E05.mp4
2025-05-25 14:23:56,458 - INFO - Processing file: To Be Hero X Episode 3 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:56,561 - INFO - Moved To Be Hero X Episode 3 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\To Be Hero X\Season 01\To Be Hero X - S01E03.mp4
2025-05-25 14:23:56,561 - INFO - Processing file: To Be Hero X Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:56,662 - INFO - Moved To Be Hero X Episode 4 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\To Be Hero X\Season 01\To Be Hero X - S01E04.mp4
2025-05-25 14:23:56,663 - INFO - Processing file: Ubel Blatt Episode 1 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:56,764 - INFO - Moved Ubel Blatt Episode 1 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E01.mp4
2025-05-25 14:23:56,764 - INFO - Processing file: Ubel Blatt Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4
2025-05-25 14:23:56,866 - INFO - Moved Ubel Blatt Episode 2 English Dubbed - WCOFun - Watch Cartoons and Anime Online in HD for Free.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\1.0 TV Shows\Ubel Blatt\Season 01\Ubel Blatt - S01E02.mp4
2025-05-25 14:23:56,867 - INFO - Processing complete. Total files: 24, Sorted: 24, Unsorted: 0.
2025-05-25 14:26:38,152 - INFO - Added TV show file: Godfather.of.Harlem.S04E03.1080p.HEVC.x265-MeGusta.WT11.mp4.mp4
2025-05-25 14:26:38,152 - INFO - Added TV show file: Kitchen Nightmares US - S09E07.mp4
2025-05-25 14:26:38,153 - INFO - Added TV show file: The.Last.of.Us.S02E03.1080p.HEVC.x265-MeGusta.WT11.mp4.mp4
2025-05-25 14:26:38,153 - INFO - Added TV show file: Andor - S02E01.mp4
2025-05-25 14:26:38,153 - INFO - Successfully added 4 TV show files
2025-05-25 14:26:39,577 - INFO - Processing file: Godfather.of.Harlem.S04E03.1080p.HEVC.x265-MeGusta.WT11.mp4.mp4
2025-05-25 14:26:39,680 - INFO - Moved Godfather.of.Harlem.S04E03.1080p.HEVC.x265-MeGusta.WT11.mp4.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows\Godfather of Harlem\Season 04\Godfather of Harlem - S04E03.mp4
2025-05-25 14:26:39,680 - INFO - Processing file: Kitchen Nightmares US - S09E07.mp4
2025-05-25 14:26:39,782 - INFO - Moved Kitchen Nightmares US - S09E07.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows\Kitchen Nightmares US\Season 09\Kitchen Nightmares US - S09E07.mp4
2025-05-25 14:26:39,782 - INFO - Processing file: The.Last.of.Us.S02E03.1080p.HEVC.x265-MeGusta.WT11.mp4.mp4
2025-05-25 14:26:39,884 - INFO - Moved The.Last.of.Us.S02E03.1080p.HEVC.x265-MeGusta.WT11.mp4.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows\The Last of Us\Season 02\The Last of Us - S02E03.mp4
2025-05-25 14:26:39,884 - INFO - Processing file: Andor - S02E01.mp4
2025-05-25 14:26:39,986 - INFO - Moved Andor - S02E01.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows\Andor\Season 02\Andor - S02E01.mp4
2025-05-25 14:26:39,986 - INFO - Processing complete. Total files: 4, Sorted: 4, Unsorted: 0.
2025-05-25 14:27:03,838 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:27:03,838 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:27:03,838 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:27:03,850 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:27:03,854 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:27:03,857 - INFO - Found 45 files in search
2025-05-25 14:27:05,859 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:27:05,860 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:27:05,860 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:27:05,864 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:27:05,867 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:27:05,870 - INFO - Found 45 files in search
2025-05-25 14:27:08,457 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:27:08,458 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:27:08,458 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:27:08,462 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:27:08,466 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:27:08,469 - INFO - Found 10 files in search
2025-05-25 14:27:09,692 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:27:09,693 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:27:09,693 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:27:09,699 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:27:09,702 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:27:09,706 - INFO - Found 10 files in search
2025-05-25 14:27:25,517 - INFO - Fetched information for: Andor - S02E01.mp4
2025-05-25 14:29:15,208 - INFO - Added movie file: Havoc (2025).mp4
2025-05-25 14:29:15,208 - INFO - Added movie file: The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4
2025-05-25 14:29:15,208 - INFO - Added movie file: A Knights War (2025).mp4
2025-05-25 14:29:15,208 - INFO - Successfully added 3 movie files
2025-05-25 14:29:19,159 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/New folder/MediaSorter/2.0 Movies
2025-05-25 14:29:19,159 - INFO - Using movie destination folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\2.0 Movies
2025-05-25 14:29:19,159 - INFO - Enabled movie destinations: New Movie Destination (C:/Users/<USER>/Desktop/02. Visual Studio Code/New folder/MediaSorter/2.0 Movies)
2025-05-25 14:29:19,159 - INFO - Starting movie processing for 3 files...
2025-05-25 14:29:19,160 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\Havoc (2025).mp4
2025-05-25 14:29:19,160 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\2.0 Movies
2025-05-25 14:29:19,405 - INFO - Successfully sorted movie: Havoc (2025).mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\2.0 Movies\Havoc (2025)\Havoc (2025).mp4
2025-05-25 14:29:19,405 - INFO - Metadata file creation is disabled
2025-05-25 14:29:19,406 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4
2025-05-25 14:29:19,406 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\2.0 Movies
2025-05-25 14:29:19,532 - INFO - Successfully sorted movie: The.Monkey.2025.1080p.WEBRip.x265-DH.WT11.mp4.mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\2.0 Movies\The Monkey (2025)\The Monkey (2025).mp4
2025-05-25 14:29:19,532 - INFO - Metadata file creation is disabled
2025-05-25 14:29:19,533 - INFO - Processing movie: C:\Users\<USER>\Desktop\New folder (6)\11.Movies\A Knights War (2025).mp4
2025-05-25 14:29:19,533 - INFO - Using movie folder: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\2.0 Movies
2025-05-25 14:29:19,683 - INFO - Successfully sorted movie: A Knights War (2025).mp4 to C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\2.0 Movies\A Knights War (2025)\A Knights War (2025).mp4
2025-05-25 14:29:19,684 - INFO - Metadata file creation is disabled
2025-05-25 14:29:19,684 - INFO - Movie processing complete. Total: 3, Sorted: 3, Unsorted: 0
2025-05-25 14:30:03,215 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:30:03,216 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:30:03,216 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:30:03,225 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:30:03,227 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:30:03,229 - INFO - Found 3 files in search
2025-05-25 14:30:08,801 - INFO - Fetched information for: A Knights War (2025).mp4
2025-05-25 14:30:14,324 - INFO - Fetched information for: Havoc (2025).mp4
2025-05-25 14:30:19,363 - INFO - Fetched information for: The Monkey (2025).mp4
2025-05-25 14:31:14,712 - INFO - Searching for subtitles: A Knights War
2025-05-25 14:31:14,712 - INFO - Searching for images: A Knights War
2025-05-25 14:31:14,712 - INFO - Fetched extras for: A Knights War (2025).mp4
2025-05-25 14:31:14,721 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:31:14,722 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:31:14,722 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:31:14,729 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:31:14,734 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:31:14,736 - INFO - Found 7 files in search
2025-05-25 14:32:27,422 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:32:27,422 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:32:27,422 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:32:27,428 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:32:27,430 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:32:27,432 - INFO - Found 0 files in search
2025-05-25 14:32:28,225 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:32:28,226 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:32:28,226 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:32:28,233 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:32:28,235 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:32:28,238 - INFO - Found 0 files in search
2025-05-25 14:32:28,829 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:32:28,830 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:32:28,830 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:32:28,836 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:32:28,838 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:32:28,841 - INFO - Found 0 files in search
2025-05-25 14:32:31,751 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:32:31,752 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:32:31,752 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:32:31,758 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:32:31,761 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:32:31,765 - INFO - Found 41 files in search
2025-05-25 14:32:49,173 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:32:49,173 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:38:11,540 - INFO - Folders verified.
2025-05-25 14:38:11,556 - INFO - MediaSorter application starting - Log test
2025-05-25 14:38:11,768 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:38:11,883 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:38:11,883 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:38:11,884 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:38:11,888 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:38:11,892 - INFO - Found 42 files in search
2025-05-25 14:38:11,909 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:38:11,910 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:38:11,940 - INFO - Created missing directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:38:11,940 - INFO - Created missing directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\2.0 Movies
2025-05-25 14:38:11,982 - INFO - Application started
2025-05-25 14:38:20,617 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:38:20,618 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:38:20,618 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:38:20,622 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:38:20,627 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:38:20,629 - INFO - Found 42 files in search
2025-05-25 14:38:21,468 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 14:38:21,469 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter, C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:38:21,469 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 14:38:21,474 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter
2025-05-25 14:38:21,478 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\New folder\MediaSorter\1.0 TV Shows
2025-05-25 14:38:21,480 - INFO - Found 42 files in search
2025-05-25 14:38:26,092 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:23:41,963 - INFO - Folders verified.
2025-05-25 16:23:41,979 - INFO - MediaSorter application starting - Log test
2025-05-25 16:23:42,202 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:23:42,323 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:23:42,324 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0004\MediaSorter
2025-05-25 16:23:42,325 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 16:23:42,329 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0004\MediaSorter
2025-05-25 16:23:42,334 - INFO - Found 41 files in search
2025-05-25 16:23:42,351 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:23:42,352 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:23:42,420 - INFO - Application started
2025-05-25 16:32:37,108 - INFO - Folders verified.
2025-05-25 16:32:37,131 - INFO - MediaSorter application starting - Log test
2025-05-25 16:32:58,887 - INFO - Folders verified.
2025-05-25 16:32:58,903 - INFO - MediaSorter application starting - Log test
2025-05-25 16:32:58,906 - INFO - Folders verified.
2025-05-25 16:33:12,133 - INFO - Folders verified.
2025-05-25 16:33:12,148 - INFO - MediaSorter application starting - Log test
2025-05-25 16:33:12,365 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:33:12,491 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:33:12,491 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0004\MediaSorter
2025-05-25 16:33:12,492 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 16:33:12,497 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0004\MediaSorter
2025-05-25 16:33:12,502 - INFO - Found 45 files in search
2025-05-25 16:33:12,529 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:33:12,530 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:33:12,619 - INFO - Application started
2025-05-25 16:33:27,797 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:33:27,797 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0004\MediaSorter
2025-05-25 16:33:27,798 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 16:33:27,804 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0004\MediaSorter
2025-05-25 16:33:27,808 - INFO - Found 45 files in search
2025-05-25 16:33:30,427 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:33:30,427 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:35:49,220 - INFO - Folders verified.
2025-05-25 16:35:49,235 - INFO - MediaSorter application starting - Log test
2025-05-25 16:35:49,452 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:35:49,567 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:35:49,567 - INFO - Scanning directories for media files: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies, C:\Users\<USER>\Desktop\02. Visual Studio Code\0004\MediaSorter
2025-05-25 16:35:49,568 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0002\MediaSorter\2.0 Movies
2025-05-25 16:35:49,572 - INFO - Scanning directory: C:\Users\<USER>\Desktop\02. Visual Studio Code\0004\MediaSorter
2025-05-25 16:35:49,577 - INFO - Found 45 files in search
2025-05-25 16:35:49,594 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:35:49,595 - INFO - Using enabled movie destination: New Movie Destination at C:/Users/<USER>/Desktop/02. Visual Studio Code/0002/MediaSorter/2.0 Movies
2025-05-25 16:35:49,675 - INFO - Application started
