# movie_handler.py v3.1

import os
import logging
from pathlib import Path
import json
import shutil
import re
from settings.settings_toggle_switch import use_movie_folder, get_movie_folder_path

def find_mediasorter_root():
    """Find the root directory of the MediaSorter application"""
    current = Path(__file__).resolve()
    while current.name != 'MediaSorter':
        current = current.parent
    return current

# LOGGING SETUP:
# Configures logging to write to Brain folder
main_folder = find_mediasorter_root()
log_dir = main_folder / '0.3 Brain Folder' / 'logs'
log_dir.mkdir(parents=True, exist_ok=True)
log_file = log_dir / 'movie_sorter_log_3.txt'

# Create a specific logger for movies
movie_logger = logging.getLogger('movie_sorter')
movie_logger.setLevel(logging.INFO)

# Remove any existing handlers to avoid duplicates
for handler in movie_logger.handlers[:]:
    movie_logger.removeHandler(handler)

# Create file handler
file_handler = logging.FileHandler(str(log_file))
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
movie_logger.addHandler(file_handler)

def get_movies_folder():
    """Get the configured movies folder from settings"""
    # First check for enabled movie destinations in custom folders
    try:
        from settings.settings_manager import load_settings
        settings = load_settings()

        # Look for enabled movie destinations
        for folder in settings.get("custom_folders", []):
            if (folder.get("sort_type") == "Movies" and
                folder.get("enabled", False) and
                folder.get("path")):

                custom_path = folder.get("path")
                if os.path.exists(custom_path):
                    movie_logger.info(f"Using enabled movie destination: {folder.get('name')} at {custom_path}")
                    return Path(custom_path)
                else:
                    movie_logger.warning(f"Enabled movie destination path not found: {custom_path}")
    except Exception as e:
        movie_logger.error(f"Error checking movie destinations: {e}")

    # Then check if we should use the global custom movie folder
    if use_movie_folder():
        custom_path = get_movie_folder_path()
        if custom_path and os.path.exists(custom_path):
            movie_logger.info(f"Using global movie folder: {custom_path}")
            return Path(custom_path)
        else:
            movie_logger.warning(f"Global movie path not found: {custom_path}")

    # Default to Movies folder in "04. Movies & TV Show/movies"
    root = find_mediasorter_root()
    movies_folder = root / "04. Movies & TV Show" / "movies"
    movies_folder.mkdir(parents=True, exist_ok=True)
    movie_logger.info(f"Using default movies folder: {movies_folder}")
    return movies_folder

def parse_movie_name(file_name):
    """Extract movie name and year from the file name."""
    # Remove file extension
    name_without_ext = os.path.splitext(file_name)[0]

    # Try different regex patterns for movie names
    patterns = [
        # Pattern 1: Movie.Name.2024.1080p
        r"^(.*?)\.?(?:\d{4})(?:\..*)?$",
        # Pattern 2: Movie Name (2024)
        r"^(.*?)\s*\(?(\d{4})\)?.*$",
        # Pattern 3: Movie.Name.720p
        r"^(.*?)(?:\.(?:480|720|1080|2160)p?).*$",
        # Pattern 4: Just clean up the name
        r"^(.+?)(?:\.|$)"
    ]

    year = ""
    name = name_without_ext

    # Try to extract year if present
    year_match = re.search(r"(?:^|\D)(\d{4})(?:\D|$)", name_without_ext)
    if year_match:
        year = f"({year_match.group(1)})"

    # Try each pattern until we get a good match
    for pattern in patterns:
        match = re.search(pattern, name_without_ext, re.IGNORECASE)
        if match:
            name = match.group(1)
            break

    # Clean up the name
    name = (name.replace('.', ' ')
                .replace('_', ' ')
                .replace('-', ' ')
                .strip())

    # Remove any trailing/leading spaces or special characters
    name = re.sub(r'[-\s]+$', '', name)
    name = re.sub(r'^[-\s]+', '', name)

    return name, year

def sort_movie(file_path, movies_folder=None):
    """Sort a movie file into the appropriate folder and format the filename."""
    try:
        file_path = Path(file_path)
        if not file_path.is_file():
            movie_logger.warning(f"Invalid file path: {file_path}")
            return False

        # Get movies folder from settings if not provided
        if movies_folder is None:
            movies_folder = get_movies_folder()
        else:
            movies_folder = Path(movies_folder)

        # Log which folder we're using
        movie_logger.info(f"Using movie folder: {movies_folder}")

        # Ensure movies folder exists
        movies_folder.mkdir(parents=True, exist_ok=True)

        # Parse movie name and year
        name, year = parse_movie_name(file_path.name)

        # Create movie folder name (always include year placeholder if none found)
        if year:
            movie_folder_name = f"{name} {year}"
        else:
            movie_folder_name = f"{name} (YEAR)"
            movie_logger.warning(f"No year found for movie: {name}. Using placeholder.")

        movie_folder = movies_folder / movie_folder_name
        formatted_name = f"{movie_folder_name}{file_path.suffix}"

        # Create movie folder
        movie_folder.mkdir(parents=True, exist_ok=True)

        # Set destination path
        destination = movie_folder / formatted_name

        # Check if destination already exists
        if destination.exists():
            movie_logger.warning(f"Movie already exists: {destination}")
            return False

        # Copy the file
        shutil.copy2(str(file_path), str(destination))
        movie_logger.info(f"Successfully sorted movie: {file_path.name} to {destination}")

        # Metadata creation has been completely disabled
        movie_logger.info("Metadata file creation is disabled")

        return True

    except Exception as e:
        movie_logger.error(f"Failed to sort movie {file_path}: {e}")
        return False

def process_gui_movies(file_paths, movies_folder=None, progress_callback=None):
    """
    Process a list of movie files from the GUI.

    Args:
        file_paths: List of file paths to process
        movies_folder: Optional destination folder for movies
        progress_callback: Optional callback function for progress updates
                          Function signature: callback(current, total, message)

    Returns:
        Tuple of (total, sorted_count, unsorted_count)
    """
    # Get movies folder if not provided
    if movies_folder is None:
        movies_folder = get_movies_folder()
        movie_logger.info(f"Using movie destination folder: {movies_folder}")
    else:
        movies_folder = Path(movies_folder)
        movie_logger.info(f"Using provided movie destination folder: {movies_folder}")

    # Log the enabled movie destinations for debugging
    try:
        from settings.settings_manager import load_settings
        settings = load_settings()
        enabled_destinations = [
            f"{folder.get('name')} ({folder.get('path')})"
            for folder in settings.get("custom_folders", [])
            if folder.get("sort_type") == "Movies" and folder.get("enabled", False)
        ]
        if enabled_destinations:
            movie_logger.info(f"Enabled movie destinations: {', '.join(enabled_destinations)}")
        else:
            movie_logger.info("No enabled movie destinations found in settings")
    except Exception as e:
        movie_logger.error(f"Error checking enabled movie destinations: {e}")

    total = len(file_paths)
    sorted_count = 0
    unsorted_count = 0

    movie_logger.info(f"Starting movie processing for {total} files...")

    for idx, file_path in enumerate(file_paths, 1):
        try:
            if progress_callback:
                progress_callback(idx, total, f"Processing: {Path(file_path).name}")

            movie_logger.info(f"Processing movie: {file_path}")

            if sort_movie(file_path, movies_folder):
                sorted_count += 1
                if progress_callback:
                    progress_callback(idx, total, f"Successfully sorted: {Path(file_path).name}")
            else:
                unsorted_count += 1
                if progress_callback:
                    progress_callback(idx, total, f"Failed to sort: {Path(file_path).name}")

        except Exception as e:
            movie_logger.error(f"Error processing movie {file_path}: {e}")
            unsorted_count += 1
            if progress_callback:
                progress_callback(idx, total, f"Error processing: {Path(file_path).name}")

    # Final progress update
    if progress_callback:
        progress_callback(total, total, "Processing complete")

    # Log final summary
    movie_logger.info(f"Movie processing complete. Total: {total}, Sorted: {sorted_count}, Unsorted: {unsorted_count}")
    return total, sorted_count, unsorted_count

# TEST CODE:
# Example usage for testing the movie sorting functionality
if __name__ == "__main__":
    # Example usage for testing
    test_file = "Harlock Space Pirate 2013.mkv"
    sort_movie(test_file)