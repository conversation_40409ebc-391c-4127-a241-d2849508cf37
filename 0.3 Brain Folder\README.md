# MediaSorter - Brain Folder

This is the main Brain folder containing all the MediaSorter application code.

## How to Run MediaSorter

### Option 1: Windows Batch File (Recommended for Windows)
Double-click `run_main.bat` in this folder.

### Option 2: Cross-Platform Python Launcher
Run from this folder:
```bash
python run_mediasorter.py
```

### Option 3: Direct Launch
Run from this folder:
```bash
python MainCodeFile.py
```

## What's New - Portable Version

✅ **Fully Portable**: MediaSorter now works from any location without hardcoded paths
✅ **Folder Name Independent**: You can rename the main MediaSorter folder to anything
✅ **Location Independent**: Move the entire folder anywhere on your system
✅ **Automatic Path Detection**: The app automatically finds its location using the Brain folder structure

## Testing

Run the portability test to verify everything works:
```bash
python test_portability.py
```

## Folder Structure

The application identifies itself by looking for this `0.3 Brain Folder` directory. The folder structure should be:

```
[Main Folder]/                 (can be renamed to anything)
├── 0.1 Sorting Folder/        (Temporary sorting location)
├── 0.2 Unsorted Folder/       (Files that couldn't be sorted)
├── 0.3 Brain Folder/          (This folder - Main application code)
│   ├── MainCodeFile.py        (Main application)
│   ├── run_main.bat           (Windows launcher)
│   ├── run_mediasorter.py     (Cross-platform launcher)
│   ├── test_portability.py    (Test script)
│   └── [other code folders]
├── 1.0 TV Shows/              (Default TV show destination)
├── 2.0 Movies/                (Default movie destination)
└── [other media folders]
```

## Requirements

- Python 3.6 or higher
- PyQt5 (automatically installed if missing)
- Internet connection (for movie/TV show metadata)

## Moving MediaSorter

To move MediaSorter to a new location:
1. Copy the entire main folder to the new location
2. Run any of the launchers from the new location
3. That's it! No configuration changes needed.

The application will automatically detect the new location and update all internal paths.
