@echo off
REM MediaSorter Universal Launcher
REM This launcher works from any location and automatically finds the Brain folder

echo MediaSorter Universal Launcher
echo ================================

REM Get the directory where this batch file is located
set "LAUNCHER_DIR=%~dp0"

REM Look for the Brain folder
if exist "%LAUNCHER_DIR%0.3 Brain Folder\MainCodeFile.py" (
    echo Found MediaSorter Brain folder
    echo Changing to Brain folder...
    cd /d "%LAUNCHER_DIR%0.3 Brain Folder"
    echo Current directory: %CD%
    echo.
    echo Starting MediaSorter...
    call run_main.bat
) else (
    echo [ERROR] MediaSorter Brain folder not found!
    echo.
    echo Please ensure this launcher is in the same directory as "0.3 Brain Folder"
    echo Launcher location: %LAUNCHER_DIR%
    echo.
    echo Looking for: %LAUNCHER_DIR%0.3 Brain Folder\MainCodeFile.py
    echo.
    pause
    exit /b 1
)

echo.
echo MediaSorter launcher finished
pause
