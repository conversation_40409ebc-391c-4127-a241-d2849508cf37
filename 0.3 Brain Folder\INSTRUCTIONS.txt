MediaSorter - How to Run After Copying to New Location
=====================================================

IMPORTANT: When you copy MediaSorter to a new location, follow these steps:

STEP 1: Copy the Complete Folder
--------------------------------
Make sure you copy the ENTIRE main MediaSorter folder, including:
- 0.1 Sorting Folder
- 0.2 Unsorted Folder  
- 0.3 Brain Folder (this folder)
- 1.0 TV Shows
- 2.0 Movies
- All other folders and files

STEP 2: Navigate to Brain Folder
--------------------------------
After copying, navigate to the "0.3 Brain Folder" in the new location.

STEP 3: Run the Application
--------------------------
Double-click one of these files:

PRIMARY METHOD:
- run_main.bat (Windows batch file)

ALTERNATIVE METHODS:
- python MainCodeFile.py (if you have command prompt open)
- python run_mediasorter.py (cross-platform launcher)

TROUBLESHOOTING:
===============

If MediaSorter doesn't start:

1. Make sure you copied the COMPLETE folder structure
2. Make sure Python is installed on your system
3. Make sure you're running from the "0.3 Brain Folder"
4. Try running as Administrator

FOLDER STRUCTURE CHECK:
======================
Your copied folder should look like this:

[Your Main Folder]/
├── 0.1 Sorting Folder/
├── 0.2 Unsorted Folder/
├── 0.3 Brain Folder/          ← YOU ARE HERE
│   ├── run_main.bat           ← DOUBLE-CLICK THIS
│   ├── MainCodeFile.py
│   ├── INSTRUCTIONS.txt       ← This file
│   └── [other files/folders]
├── 1.0 TV Shows/
├── 2.0 Movies/
└── [other media folders]

REMEMBER: Always run from the Brain folder, not the main folder!

The application is now fully portable and will work from any location
as long as you run it from the correct folder (0.3 Brain Folder).
