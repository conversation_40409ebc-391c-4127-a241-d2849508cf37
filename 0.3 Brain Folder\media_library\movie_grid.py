from PyQt5.QtWidgets import (
    QWidget, QGridLayout, QLabel, QFrame,
    QVBoxLayout, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QColor, QPalette, QFont, QPixmap
from .movie_details import MovieDetailsDialog
from .movie_player import MoviePlayer

class MoviePoster(QFrame):
    """A widget displaying a movie poster with hover effects."""
    
    clicked = pyqtSignal(dict)  # Emits movie data when clicked
    
    def __init__(self, movie_data, parent=None):
        super().__init__(parent)
        self.movie_data = movie_data
        self.setup_ui()
        
    def setup_ui(self):
        """Initialize the poster UI."""
        self.setFixedSize(200, 300)
        self.setCursor(Qt.PointingHandCursor)
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 150))
        shadow.setOffset(0, 0)
        self.setGraphicsEffect(shadow)
        
        # Set up layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # Add poster image
        self.poster_label = QLabel()
        self.poster_label.setFixedSize(200, 280)
        self.poster_label.setStyleSheet("""
            QLabel {
                background: #2A2A2A;
                border-radius: 4px;
            }
        """)
        
        # Load poster image if available
        if 'poster_path' in self.movie_data:
            pixmap = QPixmap(self.movie_data['poster_path'])
            if not pixmap.isNull():
                pixmap = pixmap.scaled(200, 280, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.poster_label.setPixmap(pixmap)
        
        layout.addWidget(self.poster_label)
        
        # Add title label
        title = QLabel(self.movie_data.get('title', 'Unknown Title'))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: bold;
                padding: 0 5px;
            }
        """)
        title.setWordWrap(True)
        layout.addWidget(title)
        
        # Set up hover animations
        self.setStyleSheet("""
            MoviePoster {
                background: transparent;
                border-radius: 4px;
                transition: transform 0.2s;
            }
            MoviePoster:hover {
                transform: scale(1.05);
            }
        """)
        
    def mousePressEvent(self, event):
        """Handle mouse press events."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.movie_data)
        super().mousePressEvent(event)

class MovieGrid(QWidget):
    """A grid of movie posters that shows details when clicked."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.movie_player = None
        
    def setup_ui(self):
        """Initialize the grid UI."""
        self.setStyleSheet("""
            QWidget {
                background: #141414;
            }
        """)
        
        self.layout = QGridLayout(self)
        self.layout.setSpacing(20)
        self.layout.setContentsMargins(20, 20, 20, 20)
        
    def load_movies(self, movies):
        """Load movies into the grid."""
        # Clear existing items
        while self.layout.count():
            item = self.layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
                
        # Add movie posters to grid
        row = 0
        col = 0
        max_cols = max(1, self.width() // 220)  # 200px poster + 20px spacing
        
        for movie in movies:
            poster = MoviePoster(movie)
            poster.clicked.connect(self.show_movie_details)
            self.layout.addWidget(poster, row, col)
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
                
    def show_movie_details(self, movie_data):
        """Show the movie details dialog."""
        dialog = MovieDetailsDialog(movie_data, self)
        dialog.play_requested.connect(self.play_movie)
        dialog.exec_()
        
    def play_movie(self, file_path):
        """Play a movie using the built-in player."""
        if self.movie_player is None:
            self.movie_player = MoviePlayer()
            
        self.movie_player.load_media(file_path)
        self.movie_player.show()
        self.movie_player.play()
        
    def resizeEvent(self, event):
        """Handle resize events to adjust grid layout."""
        super().resizeEvent(event)
        # Reload movies to adjust grid columns
        movies = []
        for i in range(self.layout.count()):
            item = self.layout.itemAt(i)
            if item and item.widget():
                poster = item.widget()
                movies.append(poster.movie_data)
        if movies:
            self.load_movies(movies) 