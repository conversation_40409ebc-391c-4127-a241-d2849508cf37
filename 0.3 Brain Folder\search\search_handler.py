# search_handler.py v1.0

import os
from pathlib import Path
import logging
from datetime import datetime
import re
from typing import List, Dict, Optional

class FileInfo:
    def __init__(self, path):
        self.path = Path(path)
        self.name = self.path.name
        self.size = self.path.stat().st_size
        self.modified = datetime.fromtimestamp(self.path.stat().st_mtime)
        self.type = self.get_file_type()
        
    def get_file_type(self):
        """Determine the type of file based on name and location"""
        suffix = self.path.suffix.lower()
        name = self.path.name.lower()
        
        # Check for TV Show patterns
        if re.search(r's\d{2}e\d{2}|season.*episode', name, re.IGNORECASE):
            return "TV Show"
        # Check for movie patterns
        elif re.search(r'\(\d{4}\)|\d{4}', name) and suffix in {'.mp4', '.mkv', '.avi'}:
            return "Movie"
        # Check for common video formats
        elif suffix in {'.mp4', '.mkv', '.avi', '.mov', '.wmv'}:
            return "Video"
        else:
            return "Other"
    
    def matches_search(self, query):
        """Check if file matches search query"""
        if not query:
            return True
            
        query = query.lower()
        return (query in self.name.lower() or
                query in self.type.lower())
    
    def matches_filter(self, file_type=None, min_size=None, max_size=None, 
                      start_date=None, end_date=None):
        """Check if file matches filter criteria"""
        if file_type and self.type != file_type:
            return False
            
        if min_size and self.size < min_size:
            return False
            
        if max_size and self.size > max_size:
            return False
            
        if start_date and self.modified.date() < start_date:
            return False
            
        if end_date and self.modified.date() > end_date:
            return False
            
        return True

class SearchHandler:
    def __init__(self, root_folder: str):
        self.root_folder = root_folder
        self.file_types = set()
        self.scan_files()

    def scan_files(self) -> None:
        """Scan the root folder and update the list of available file types."""
        self.file_types.clear()
        for root, _, files in os.walk(self.root_folder):
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                if ext:  # Only add non-empty extensions
                    self.file_types.add(ext[1:])  # Remove the dot from extension

    def get_file_types(self) -> List[str]:
        """Return the list of available file types."""
        return sorted(list(self.file_types))

    def search_files(self, 
                    query: str = "", 
                    file_type: Optional[str] = None,
                    min_size: Optional[int] = None,
                    max_size: Optional[int] = None,
                    date_from: Optional[datetime] = None,
                    date_to: Optional[datetime] = None) -> List[Dict]:
        """
        Search for files based on given criteria.
        Returns a list of dictionaries containing file information.
        """
        results = []
        query = query.lower()

        for root, _, files in os.walk(self.root_folder):
            for file in files:
                file_path = os.path.join(root, file)
                
                # Basic name matching
                if query and query not in file.lower():
                    continue

                # File type filtering
                ext = os.path.splitext(file)[1].lower()
                if file_type and ext[1:] != file_type:
                    continue

                try:
                    stats = os.stat(file_path)
                    size = stats.st_size
                    modified_time = datetime.fromtimestamp(stats.st_mtime)

                    # Size filtering
                    if min_size is not None and size < min_size:
                        continue
                    if max_size is not None and size > max_size:
                        continue

                    # Date filtering
                    if date_from is not None and modified_time < date_from:
                        continue
                    if date_to is not None and modified_time > date_to:
                        continue

                    # Add file to results
                    results.append({
                        'name': file,
                        'path': file_path,
                        'type': ext[1:] if ext else '',
                        'size': size,
                        'modified': modified_time
                    })

                except (OSError, PermissionError):
                    continue  # Skip files we can't access

        return results

    def parse_size_filter(self, size_filter: str) -> tuple[Optional[int], Optional[int]]:
        """
        Parse size filter string into min and max values in bytes.
        Format examples: '>1MB', '<500KB', '1MB-5MB'
        """
        if not size_filter or size_filter.lower() == 'any':
            return None, None

        size_units = {
            'b': 1,
            'kb': 1024,
            'mb': 1024 * 1024,
            'gb': 1024 * 1024 * 1024,
            'tb': 1024 * 1024 * 1024 * 1024
        }

        def parse_size(size_str: str) -> int:
            size_str = size_str.strip().lower()
            for unit, multiplier in size_units.items():
                if size_str.endswith(unit):
                    try:
                        value = float(size_str[:-len(unit)])
                        return int(value * multiplier)
                    except ValueError:
                        return 0
            return 0

        if '-' in size_filter:
            min_str, max_str = size_filter.split('-')
            return parse_size(min_str), parse_size(max_str)
        elif size_filter.startswith('>'):
            return parse_size(size_filter[1:]), None
        elif size_filter.startswith('<'):
            return None, parse_size(size_filter[1:])
        else:
            size = parse_size(size_filter)
            return size, size

    def parse_date_filter(self, date_filter: str) -> tuple[Optional[datetime], Optional[datetime]]:
        """
        Parse date filter string into from and to datetime objects.
        Format examples: '>2023-01-01', '<2023-12-31', '2023-01-01-2023-12-31'
        """
        if not date_filter or date_filter.lower() == 'any':
            return None, None

        def parse_date(date_str: str) -> Optional[datetime]:
            try:
                return datetime.strptime(date_str.strip(), '%Y-%m-%d')
            except ValueError:
                return None

        if '-' in date_filter:
            if date_filter.count('-') == 3:  # Range format
                from_str, to_str = date_filter.split('-', 1)
                return parse_date(from_str), parse_date(to_str)
            else:  # Single date
                return parse_date(date_filter), parse_date(date_filter)
        elif date_filter.startswith('>'):
            return parse_date(date_filter[1:]), None
        elif date_filter.startswith('<'):
            return None, parse_date(date_filter[1:])
        else:
            date = parse_date(date_filter)
            return date, date

    def scan_folders(self, root_path):
        """Scan folders for sorted files"""
        root = Path(root_path)
        self.sorted_files = []
        
        try:
            # Scan Movies folder
            movies_path = root / "Movies"
            if movies_path.exists():
                for file_path in movies_path.rglob('*'):
                    if file_path.is_file():
                        self.sorted_files.append(FileInfo(file_path))
            
            # Scan TV Shows/Series folder
            tv_path = root / "0.1 Sorting Folder"
            if tv_path.exists():
                for file_path in tv_path.rglob('*'):
                    if file_path.is_file():
                        self.sorted_files.append(FileInfo(file_path))
                        
        except Exception as e:
            logging.error(f"Error scanning folders: {e}")
    
    def search(self, query=None, file_type=None, min_size=None, max_size=None,
               start_date=None, end_date=None, sort_by='name', reverse=False):
        """Search files with filters and sorting"""
        results = []
        
        # Apply search and filters
        for file_info in self.sorted_files:
            if (file_info.matches_search(query) and 
                file_info.matches_filter(file_type, min_size, max_size, 
                                      start_date, end_date)):
                results.append(file_info)
        
        # Sort results
        if sort_by == 'name':
            results.sort(key=lambda x: x.name.lower(), reverse=reverse)
        elif sort_by == 'size':
            results.sort(key=lambda x: x.size, reverse=reverse)
        elif sort_by == 'date':
            results.sort(key=lambda x: x.modified, reverse=reverse)
        elif sort_by == 'type':
            results.sort(key=lambda x: x.type, reverse=reverse)
        
        return results
    
    def add_recent_file(self, file_path):
        """Add a file to recent files list"""
        file_info = FileInfo(file_path)
        self.recent_files.insert(0, file_info)
        
        # Keep only the most recent files
        if len(self.recent_files) > self.max_recent:
            self.recent_files = self.recent_files[:self.max_recent]
    
    def get_recent_files(self, limit=10):
        """Get the most recently processed files"""
        return self.recent_files[:limit]
    
    def get_file_types(self):
        """Get list of unique file types"""
        return sorted(set(f.type for f in self.sorted_files)) 