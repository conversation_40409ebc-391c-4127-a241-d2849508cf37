#!/usr/bin/env python3
"""
MediaSorter Cross-Platform Launcher
This script runs MediaSorter from the Brain folder.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("MediaSorter Cross-Platform Launcher")
    print("=" * 35)
    
    # We're already in the Brain folder
    brain_folder = Path(__file__).resolve().parent
    
    # Find the main Python file
    main_file = brain_folder / "MainCodeFile.py"
    if not main_file.exists():
        print(f"[ERROR] MainCodeFile.py not found in {brain_folder}")
        input("Press Enter to exit...")
        sys.exit(1)
    
    print(f"Found MediaSorter in: {brain_folder.parent}")
    print("Starting MediaSorter...")
    print()
    
    # We're already in the correct directory
    
    # Run the main Python file
    try:
        subprocess.run([sys.executable, "MainCodeFile.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] MediaSorter failed to start: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nMediaSorter was interrupted by user")
    except Exception as e:
        print(f"[ERROR] Unexpected error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
