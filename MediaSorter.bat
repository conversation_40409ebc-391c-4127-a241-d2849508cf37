@echo off
REM MediaSorter Launcher - Run from anywhere
REM This script automatically finds and runs MediaSorter regardless of folder location

echo MediaSorter Launcher
echo ===================

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"

REM Look for the Brain folder
if exist "%SCRIPT_DIR%0.3 Brain Folder\run_main.bat" (
    echo Found MediaSorter in: %SCRIPT_DIR%
    echo Starting MediaSorter...
    echo.
    cd /d "%SCRIPT_DIR%0.3 Brain Folder"
    call run_main.bat
) else (
    echo [ERROR] MediaSorter Brain folder not found!
    echo Please ensure this launcher is in the same directory as "0.3 Brain Folder"
    echo Current directory: %SCRIPT_DIR%
    pause
    exit /b 1
)

pause
