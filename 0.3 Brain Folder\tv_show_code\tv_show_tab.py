from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QScrollArea, QGroupBox, QFrame, QSizePolicy,
                             QFileDialog, QListWidget, QMessageBox, QAbstractItemView)
from PyQt5.QtCore import Qt
from .tv_show_destinations import TVShowDestinationWidget, create_new_tv_destination, get_tv_destinations
from settings.settings_manager import load_settings, save_settings
from .tv_show import process_files
import logging
from pathlib import Path
import re
import shutil

class TVShowListWidget(QListWidget):
    """Custom QListWidget with drag-and-drop functionality for TV show files."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDragDropMode(QAbstractItemView.DropOnly)
        self.setAlternatingRowColors(True)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.main_window = None
        self.file_paths = []
        self.is_dragging = False
        self.tv_extensions = {'.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg', '.3gp'}
        # Set dark style for all states
        self.setStyleSheet("""
            QListWidget {
                background-color: #181a20;
                color: #fff;
                border: 2px dashed #888;
                border-radius: 5px;
                padding: 10px;
            }
            QListWidget::item {
                background: #181a20;
                color: #fff;
            }
            QListWidget::item:selected {
                background: #23272e;
                color: #fff;
            }
            QListWidget::item:hover {
                background: #23272e;
                color: #fff;
            }
        """)

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            self.is_dragging = True
            event.accept()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        self.is_dragging = False
        super().dragLeaveEvent(event)

    def dragMoveEvent(self, event):
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        self.is_dragging = False
        if not event.mimeData().hasUrls():
            event.ignore()
            return
            
        event.accept()
        try:
            file_paths = []
            skipped_files = []
            
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                path = Path(file_path)
                if path.is_file():
                    if path.suffix.lower() in self.tv_extensions:
                        if str(path) not in self.file_paths:  # Prevent duplicates
                            file_paths.append(str(path))
                            self.addItem(path.name)
                            if self.main_window:
                                self.main_window.log_action(f"Added TV show file: {path.name}")
                    else:
                        skipped_files.append(path.name)
            
            # Add all valid files at once
            if file_paths:
                self.file_paths.extend(file_paths)
                if self.main_window:
                    self.main_window.log_action(f"Successfully added {len(file_paths)} TV show files")
            
            # Report skipped files
            if skipped_files and self.main_window:
                self.main_window.log_action(f"Skipped {len(skipped_files)} non-video files")
                
        except Exception as e:
            if self.main_window:
                self.main_window.log_action(f"Error adding TV show files: {e}")

    def add_files(self, file_paths):
        """Add files to the list widget with error handling"""
        try:
            added_count = 0
            skipped_count = 0
            
            for file_path in file_paths:
                path = Path(file_path)
                if path.is_file():
                    if path.suffix.lower() in self.tv_extensions:
                        if str(path) not in self.file_paths:  # Prevent duplicates
                            self.file_paths.append(str(path))
                            self.addItem(path.name)
                            added_count += 1
                    else:
                        skipped_count += 1
            
            if self.main_window:
                if added_count > 0:
                    self.main_window.log_action(f"Successfully added {added_count} TV show files")
                if skipped_count > 0:
                    self.main_window.log_action(f"Skipped {skipped_count} non-video files")
                
        except Exception as e:
            if self.main_window:
                self.main_window.log_action(f"Error adding TV show files: {e}")

    def clear(self):
        """Override clear to also clear file paths"""
        super().clear()
        self.file_paths = []

    def set_main_window(self, main_window):
        self.main_window = main_window

class TVShowTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings = load_settings()
        self.folder_widgets = []
        self.file_list = TVShowListWidget()  # Use our custom widget
        self.file_list.set_main_window(parent)  # Set the main window reference
        self.setup_ui()
        self.load_destinations()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        self.setLayout(layout)

        # Header section
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #23272e;
                border-radius: 5px;
                padding: 10px;
            }
            QLabel {
                color: #f1f1f1;
                font-size: 14px;
            }
        """)
        header_layout = QVBoxLayout()
        header_frame.setLayout(header_layout)

        title_label = QLabel("TV Show Sorting")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #fff;")
        header_layout.addWidget(title_label)

        desc_label = QLabel("Sort your TV show files into organized folders automatically.")
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #e0e0e0;")
        header_layout.addWidget(desc_label)

        layout.addWidget(header_frame)

        # Content layout
        content_layout = QHBoxLayout()
        content_layout.setSpacing(10)

        # Left side - Destinations
        destinations_group = QGroupBox("TV Show Sort Destinations")
        destinations_group.setMinimumWidth(420)
        destinations_group.setMaximumWidth(520)
        destinations_group.setStyleSheet("""
            QGroupBox {
                background-color: #23272e;
                border: 2px solid #444;
                border-radius: 8px;
                margin-top: 8px;
                color: #fff;
            }
        """)
        destinations_layout = QVBoxLayout()
        destinations_group.setLayout(destinations_layout)

        # Scroll area for destinations
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        scroll_content = QWidget()
        self.destinations_layout = QVBoxLayout()
        self.destinations_layout.setContentsMargins(5, 5, 5, 5)
        self.destinations_layout.setSpacing(5)
        scroll_content.setLayout(self.destinations_layout)
        # Set size policy to prevent squishing
        scroll_content.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Fixed)
        self.scroll_content = scroll_content  # Save for later min height update
        scroll_area.setWidget(scroll_content)
        destinations_layout.addWidget(scroll_area)

        # Add new destination and save buttons
        button_layout = QHBoxLayout()
        add_button = QPushButton("Add New TV Show Destination")
        add_button.clicked.connect(self.add_new_destination)
        button_layout.addWidget(add_button)
        save_button = QPushButton("Save Destinations")
        save_button.clicked.connect(self.save_destinations)
        button_layout.addWidget(save_button)
        destinations_layout.addLayout(button_layout)

        content_layout.addWidget(destinations_group)

        # Right side - File list and controls
        right_layout = QVBoxLayout()
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background-color: #181a20;
                border: 2px solid #444;
                border-radius: 8px;
            }
        """)
        right_frame_layout = QVBoxLayout()
        right_frame.setLayout(right_frame_layout)
        
        # File list widget
        right_frame_layout.addWidget(self.file_list)
        
        # File drop instructions
        drop_label = QLabel("Drop TV show files here or click 'Add Files'")
        drop_label.setAlignment(Qt.AlignCenter)
        drop_label.setStyleSheet("""
            QLabel {
                padding: 20px;
                background-color: #23272e;
                border: 2px dashed #888;
                border-radius: 5px;
                color: #fff;
            }
        """)
        right_frame_layout.addWidget(drop_label)
        
        # Add files button
        add_files_button = QPushButton("Add Files")
        add_files_button.clicked.connect(self.add_files)
        right_frame_layout.addWidget(add_files_button)
        
        # Sort button
        sort_button = QPushButton("Sort Files")
        sort_button.clicked.connect(self.sort_files)
        right_frame_layout.addWidget(sort_button)
        
        right_layout.addWidget(right_frame)
        content_layout.addLayout(right_layout)
        layout.addLayout(content_layout)

    def load_destinations(self):
        # Clear existing widgets
        for widget in self.folder_widgets:
            widget.deleteLater()
        self.folder_widgets.clear()

        # Load destinations from settings
        destinations = get_tv_destinations()
        for dest in destinations:
            widget = TVShowDestinationWidget(dest, self.settings)
            self.folder_widgets.append(widget)
            self.destinations_layout.addWidget(widget)

        # Dynamically set the minimum height of the scroll content
        widget_height = 90  # Must match TVShowDestinationWidget fixed height
        spacing = self.destinations_layout.spacing()
        total_height = len(self.folder_widgets) * (widget_height + spacing)
        self.scroll_content.setMinimumHeight(total_height)
        # Always scroll to the top after loading
        parent_scroll = self.scroll_content.parent()
        if hasattr(parent_scroll, 'verticalScrollBar'):
            parent_scroll.verticalScrollBar().setValue(0)

    def get_next_number(self):
        # Find the lowest available number
        used_numbers = set()
        for widget in self.folder_widgets:
            if hasattr(widget, 'number'):
                used_numbers.add(widget.number)
            elif 'number' in widget.folder_data:
                used_numbers.add(widget.folder_data['number'])
        n = 1
        while n in used_numbers:
            n += 1
        return n

    def add_new_destination(self):
        number = self.get_next_number()
        new_dest = create_new_tv_destination(number)
        widget = TVShowDestinationWidget(new_dest, self.settings)
        widget.number = number
        self.folder_widgets.append(widget)
        self.destinations_layout.addWidget(widget)
        # Update scroll content min height
        widget_height = 90
        spacing = self.destinations_layout.spacing()
        total_height = len(self.folder_widgets) * (widget_height + spacing)
        self.scroll_content.setMinimumHeight(total_height)
        widget.save_changes()
        # Scroll to the top after adding
        parent_scroll = self.scroll_content.parent()
        if hasattr(parent_scroll, 'verticalScrollBar'):
            parent_scroll.verticalScrollBar().setValue(0)

    def save_destinations(self):
        # Save the current list of destinations (with numbers) to settings
        custom_folders = []
        for widget in self.folder_widgets:
            data = widget.folder_data.copy()
            if hasattr(widget, 'number'):
                data['number'] = widget.number
            elif 'number' not in data:
                data['number'] = 0
            custom_folders.append(data)
        self.settings['custom_folders'] = custom_folders
        save_settings(self.settings)

    def add_files(self):
        """Open a file dialog to select TV show files to sort."""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("Video Files (*.mp4 *.mkv *.avi *.mov *.wmv *.flv *.webm *.m4v *.mpg *.mpeg *.3gp)")
        
        if file_dialog.exec_():
            selected_files = file_dialog.selectedFiles()
            self.file_list.add_files(selected_files)

    def sort_files(self):
        """Process the files in the list using the tv_show.py process_files function."""
        if not self.file_list.file_paths:
            QMessageBox.information(self, "No Files", "No files to sort. Please add files first.")
            return
        try:
            from .tv_show import process_files
            total, sorted_count, unsorted_count = process_files(self.file_list.file_paths)
            self.file_list.clear()
            self.file_list.file_paths = []
            QMessageBox.information(
                self, 
                "Sorting Complete", 
                f"TV shows sorted successfully.\nTotal: {total}\nSorted: {sorted_count}\nUnsorted: {unsorted_count}"
            )
        except Exception as e:
            logging.error(f"Error sorting files: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error sorting files: {str(e)}") 