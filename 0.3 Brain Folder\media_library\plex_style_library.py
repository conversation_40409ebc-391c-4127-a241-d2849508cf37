from PyQt5.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QLineEdit, QScrollArea, QFrame,
    QGridLayout, QSizePolicy, QFileDialog, QMessageBox,
    QComboBox, QSpacerItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QPixmap, QFont, QColor, QPalette, QIcon
from pathlib import Path
import os
import json
import shutil
import re
from .movie_details import MovieDetailsDialog
from .movie_player import MoviePlayer

class MoviePoster(QFrame):
    """A movie poster widget with hover effects."""

    clicked = pyqtSignal(dict)  # Signal emitted when poster is clicked

    def __init__(self, movie_data, parent=None):
        super().__init__(parent)
        self.movie_data = movie_data
        self.setup_ui()

    def setup_ui(self):
        """Set up the poster UI."""
        self.setFixedSize(180, 270)
        self.setCursor(Qt.PointingHandCursor)

        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 5)
        layout.setSpacing(5)

        # Poster image
        self.poster_label = QLabel()
        self.poster_label.setFixedSize(180, 240)
        self.poster_label.setScaledContents(True)
        self.poster_label.setStyleSheet("""
            QLabel {
                background-color: #2d2d2d;
                border-radius: 4px;
            }
        """)

        # Load poster image if available
        poster_path = self.movie_data.get('poster_path')
        if poster_path and os.path.exists(poster_path):
            pixmap = QPixmap(poster_path)
            self.poster_label.setPixmap(pixmap)
        else:
            # Use placeholder image
            self.poster_label.setText("No Image")
            self.poster_label.setAlignment(Qt.AlignCenter)

        layout.addWidget(self.poster_label)

        # Title label
        title = self.movie_data.get('title', 'Unknown')
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: bold;
            }
        """)
        layout.addWidget(title_label)

        # Set frame style
        self.setStyleSheet("""
            MoviePoster {
                background-color: transparent;
                border-radius: 4px;
            }
            MoviePoster:hover {
                background-color: #3d3d3d;
                transform: scale(1.05);
            }
        """)

    def mousePressEvent(self, event):
        """Handle mouse press events."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.movie_data)
        super().mousePressEvent(event)

class CategoryHeader(QWidget):
    """A header for a category of movies."""

    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.title = title
        self.setup_ui()

    def setup_ui(self):
        """Set up the header UI."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 10, 0, 5)

        # Title label
        title_label = QLabel(self.title)
        title_label.setFont(QFont('Arial', 18, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        layout.addWidget(title_label)

        # Add spacer
        layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # View All button
        view_all = QPushButton("View All")
        view_all.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #0078d7;
                border: none;
                font-weight: bold;
            }
            QPushButton:hover {
                color: #1e90ff;
            }
        """)
        layout.addWidget(view_all)

class PlexStyleMovieLibrary(QWidget):
    """A Plex-style movie library widget."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.movie_player = None
        self.movies_by_category = {}
        self.all_movies = []
        self.setup_ui()

    def setup_ui(self):
        """Set up the library UI."""
        # Set dark theme
        self.setStyleSheet("""
            QWidget {
                background-color: #1a1a1a;
                color: white;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QLineEdit {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3d3d3d;
                border-radius: 4px;
                padding: 8px;
            }
            QPushButton {
                background-color: #0078d7;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1e90ff;
            }
            QComboBox {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3d3d3d;
                border-radius: 4px;
                padding: 8px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox QAbstractItemView {
                background-color: #2d2d2d;
                color: white;
                selection-background-color: #0078d7;
            }
        """)

        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)

        # Header with search and controls
        header_layout = QHBoxLayout()

        # Search bar
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search movies...")
        self.search_input.textChanged.connect(self.search_movies)
        self.search_input.setMinimumWidth(300)
        header_layout.addWidget(self.search_input)

        # Filter dropdown
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["All", "Recently Added", "Movies", "TV Shows"])
        self.filter_combo.currentTextChanged.connect(self.apply_filter)
        header_layout.addWidget(self.filter_combo)

        # Add folder button
        add_folder_btn = QPushButton("Add Folder")
        add_folder_btn.clicked.connect(self.add_folder)
        header_layout.addWidget(add_folder_btn)

        # Scan library button
        scan_btn = QPushButton("Scan Library")
        scan_btn.clicked.connect(self.scan_library)
        header_layout.addWidget(scan_btn)

        layout.addLayout(header_layout)

        # Create scroll area for content
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Content widget
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(20)

        scroll.setWidget(self.content_widget)
        layout.addWidget(scroll)

        # Load movies
        self.load_movies()

    def load_movies(self):
        """Load movies from the configured folders."""
        try:
            # Clear existing content
            self.clear_content()

            # Get movies folder from settings
            from movie_code.movie_handler import get_movies_folder
            movies_folder = get_movies_folder()

            if not movies_folder or not os.path.exists(movies_folder):
                return

            # Scan for movies
            self.all_movies = []
            self.movies_by_category = {
                "Recently Added": [],
                "All Movies": [],
                "Action": [],
                "Comedy": [],
                "Drama": [],
                "Sci-Fi": []
            }

            # Walk through the movies folder
            for root, dirs, files in os.walk(movies_folder):
                for file in files:
                    if file.endswith(('.mp4', '.mkv', '.avi', '.mov')):
                        file_path = os.path.join(root, file)
                        movie_folder = Path(os.path.dirname(file_path))

                        # Extract movie data from folder name
                        folder_name = movie_folder.name
                        movie_data = {
                            'title': folder_name,
                            'file_path': file_path
                        }

                        # Try to extract year from folder name (e.g., "Movie Name (2023)")
                        year_match = re.search(r'\((\d{4})\)$', folder_name)
                        if year_match:
                            movie_data['year'] = year_match.group(1)
                            # Remove year from title
                            movie_data['title'] = folder_name.replace(f" ({year_match.group(1)})", "")

                        # Check if metadata folder exists (for backward compatibility)
                        metadata_folder = movie_folder / "metadata"
                        if metadata_folder.exists():
                            # Try to load metadata if it exists
                            metadata_file = metadata_folder / "movie_info.json"
                            if metadata_file.exists():
                                try:
                                    with open(metadata_file, 'r', encoding='utf-8') as f:
                                        metadata = json.load(f)
                                        movie_data.update(metadata)
                                except:
                                    pass

                            # Add poster path if available
                            poster_path = metadata_folder / "poster.jpg"
                            if poster_path.exists():
                                movie_data['poster_path'] = str(poster_path)

                        # Add to all movies
                        self.all_movies.append(movie_data)

                        # Add to categories
                        self.movies_by_category["All Movies"].append(movie_data)

                        # Add to recently added (sort later)
                        self.movies_by_category["Recently Added"].append(movie_data)

                        # Add to genres if available
                        genres = movie_data.get('genres', [])
                        for genre in genres:
                            if genre in self.movies_by_category:
                                self.movies_by_category[genre].append(movie_data)
                            else:
                                self.movies_by_category[genre] = [movie_data]

            # Sort recently added by modification time
            self.movies_by_category["Recently Added"].sort(
                key=lambda m: os.path.getmtime(m['file_path']),
                reverse=True
            )

            # Limit to 10 movies
            self.movies_by_category["Recently Added"] = self.movies_by_category["Recently Added"][:10]

            # Display categories
            self.display_categories()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load movies: {str(e)}")

    def display_categories(self):
        """Display movie categories."""
        # Add Recently Added section
        if self.movies_by_category["Recently Added"]:
            self.add_category("Recently Added", self.movies_by_category["Recently Added"])

        # Add genres with movies
        for category, movies in self.movies_by_category.items():
            if category not in ["Recently Added", "All Movies"] and movies:
                self.add_category(category, movies)

    def add_category(self, category_name, movies):
        """Add a category of movies to the layout."""
        # Add header
        header = CategoryHeader(category_name)
        self.content_layout.addWidget(header)

        # Create horizontal layout for movies
        movie_layout = QHBoxLayout()
        movie_layout.setSpacing(10)

        # Add movie posters
        for movie in movies[:6]:  # Show up to 6 movies per row
            poster = MoviePoster(movie)
            poster.clicked.connect(self.show_movie_details)
            movie_layout.addWidget(poster)

        # Add spacer to push movies to the left
        movie_layout.addStretch()

        self.content_layout.addLayout(movie_layout)

    def clear_content(self):
        """Clear the content layout."""
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
            elif item.layout():
                self.clear_layout(item.layout())

    def clear_layout(self, layout):
        """Recursively clear a layout."""
        while layout.count():
            item = layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
            elif item.layout():
                self.clear_layout(item.layout())

    def show_movie_details(self, movie_data):
        """Show the movie details dialog."""
        dialog = MovieDetailsDialog(movie_data, self)
        dialog.play_requested.connect(self.play_movie)
        dialog.exec_()

    def play_movie(self, file_path):
        """Play a movie using the built-in player."""
        if self.movie_player is None:
            self.movie_player = MoviePlayer()

        self.movie_player.load_media(file_path)
        self.movie_player.show()
        self.movie_player.play()

    def search_movies(self, query):
        """Search movies by title or metadata."""
        if not query:
            # If search is empty, show all categories
            self.clear_content()
            self.display_categories()
            return

        # Filter movies by search query
        query = query.lower()
        results = []

        for movie in self.all_movies:
            title = movie.get('title', '').lower()
            overview = movie.get('overview', '').lower()

            if query in title or query in overview:
                results.append(movie)

        # Display search results
        self.clear_content()

        if results:
            # Add search results header
            header = CategoryHeader(f"Search Results for '{query}'")
            self.content_layout.addWidget(header)

            # Create grid layout for results
            grid = QGridLayout()
            grid.setSpacing(10)

            # Add movie posters to grid
            row, col = 0, 0
            max_cols = 5

            for movie in results:
                poster = MoviePoster(movie)
                poster.clicked.connect(self.show_movie_details)
                grid.addWidget(poster, row, col)

                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1

            self.content_layout.addLayout(grid)
        else:
            # No results found
            no_results = QLabel("No movies found matching your search.")
            no_results.setAlignment(Qt.AlignCenter)
            no_results.setStyleSheet("font-size: 16px; color: #888888; padding: 40px;")
            self.content_layout.addWidget(no_results)

    def apply_filter(self, filter_text):
        """Apply a filter to the movie library."""
        # Reset search
        self.search_input.clear()

        # Apply filter
        if filter_text == "All":
            self.clear_content()
            self.display_categories()
        elif filter_text == "Recently Added":
            self.clear_content()
            if self.movies_by_category["Recently Added"]:
                self.add_category("Recently Added", self.movies_by_category["Recently Added"])
        else:
            # Filter by category
            self.clear_content()
            for category, movies in self.movies_by_category.items():
                if category == filter_text and movies:
                    self.add_category(category, movies)

    def add_folder(self):
        """Add a folder of movies to the library."""
        folder = QFileDialog.getExistingDirectory(
            self, "Select Movie Folder", "",
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )

        if not folder:
            return

        try:
            # Get destination folder
            from movie_code.movie_handler import get_movies_folder
            dest_folder = get_movies_folder()

            if not dest_folder:
                QMessageBox.warning(self, "Error", "No movie destination folder configured.")
                return

            # Scan the selected folder for movie files
            movie_files = []
            for root, dirs, files in os.walk(folder):
                for file in files:
                    if file.endswith(('.mp4', '.mkv', '.avi', '.mov')):
                        movie_files.append(os.path.join(root, file))

            if not movie_files:
                QMessageBox.information(self, "No Movies", "No movie files found in the selected folder.")
                return

            # Ask for confirmation
            confirm = QMessageBox.question(
                self, "Import Movies",
                f"Found {len(movie_files)} movie files. Import them to your library?",
                QMessageBox.Yes | QMessageBox.No
            )

            if confirm == QMessageBox.Yes:
                # Import movies
                from movie_code.movie_handler import process_gui_movies
                total, sorted_count, unsorted_count = process_gui_movies(movie_files)

                QMessageBox.information(
                    self, "Import Complete",
                    f"Successfully imported {sorted_count} movies.\n"
                    f"Failed to import {unsorted_count} movies."
                )

                # Reload library
                self.load_movies()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to import movies: {str(e)}")

    def scan_library(self):
        """Scan the library for new movies."""
        try:
            # Reload movies
            self.load_movies()
            QMessageBox.information(self, "Scan Complete", "Library scan completed successfully.")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to scan library: {str(e)}")
