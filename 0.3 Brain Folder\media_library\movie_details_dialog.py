from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QScrollArea, QWidget, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor, QLinearGradient, QPalette, QFont, QPixmap
from .movie_player import MoviePlayer
import sys
import subprocess

class MovieDetailsDialog(QDialog):
    """A Netflix-inspired movie details dialog."""
    
    play_movie = pyqtSignal(str)  # Emits the movie file path when play is clicked
    
    def __init__(self, movie_folder, movie_info, parent=None):
        super().__init__(parent)
        self.movie_folder = movie_folder
        self.movie_info = movie_info
        self.setWindowTitle(movie_info.get('title', 'Movie Details'))
        self.setMinimumSize(800, 600)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the user interface"""
        layout = QVBoxLayout(self)
        
        # Create a scroll area for the content
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Create content widget
        content = QWidget()
        content_layout = QVBoxLayout(content)
        
        # Add backdrop
        backdrop_file = self.movie_folder / f"{self.movie_folder.name}_info" / "backdrops" / "main_backdrop.jpg"
        if backdrop_file.exists():
            backdrop_label = QLabel()
            pixmap = QPixmap(str(backdrop_file))
            scaled_pixmap = pixmap.scaled(780, 439, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            backdrop_label.setPixmap(scaled_pixmap)
            backdrop_label.setStyleSheet("border-radius: 10px;")
            content_layout.addWidget(backdrop_label)
        
        # Title and year
        title_year = QLabel(
            f"{self.movie_info.get('title', 'Unknown')} "
            f"({self.movie_info.get('release_date', '')[:4]})"
        )
        title_year.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                margin-top: 20px;
            }
        """)
        content_layout.addWidget(title_year)
        
        # Rating and runtime
        info_line = QLabel(
            f"Rating: {self.movie_info.get('vote_average', 'N/A')}/10 • "
            f"Runtime: {self.movie_info.get('runtime', 'N/A')} minutes"
        )
        info_line.setStyleSheet("color: #888888;")
        content_layout.addWidget(info_line)
        
        # Genres
        genres = ", ".join(g['name'] for g in self.movie_info.get('genres', []))
        genre_label = QLabel(f"Genres: {genres}")
        genre_label.setStyleSheet("color: #888888;")
        content_layout.addWidget(genre_label)
        
        # Overview
        overview_title = QLabel("Overview")
        overview_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                margin-top: 20px;
            }
        """)
        content_layout.addWidget(overview_title)
        
        overview = QLabel(self.movie_info.get('overview', 'No overview available.'))
        overview.setWordWrap(True)
        overview.setStyleSheet("color: white; margin-bottom: 20px;")
        content_layout.addWidget(overview)
        
        # Cast
        cast_title = QLabel("Cast")
        cast_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
            }
        """)
        content_layout.addWidget(cast_title)
        
        # Cast grid
        cast_layout = QHBoxLayout()
        cast = self.movie_info.get('credits', {}).get('cast', [])[:6]  # Top 6 cast members
        
        for actor in cast:
            actor_widget = QFrame()
            actor_layout = QVBoxLayout(actor_widget)
            
            name_label = QLabel(actor['name'])
            name_label.setStyleSheet("color: white; font-weight: bold;")
            name_label.setAlignment(Qt.AlignCenter)
            
            char_label = QLabel(actor['character'])
            char_label.setStyleSheet("color: #888888;")
            char_label.setAlignment(Qt.AlignCenter)
            
            actor_layout.addWidget(name_label)
            actor_layout.addWidget(char_label)
            cast_layout.addWidget(actor_widget)
            
        content_layout.addLayout(cast_layout)
        
        # Play button
        play_button = QPushButton("Play Movie")
        play_button.setStyleSheet("""
            QPushButton {
                background-color: #0078D4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #106EBE;
            }
        """)
        play_button.clicked.connect(self.play_movie)
        content_layout.addWidget(play_button)
        
        # Add content to scroll area
        scroll.setWidget(content)
        layout.addWidget(scroll)
        
        # Style the dialog
        self.setStyleSheet("""
            QDialog {
                background-color: #1a1a1a;
            }
            QScrollArea {
                border: none;
                background-color: #1a1a1a;
            }
            QScrollBar:vertical {
                border: none;
                background: #2d2d2d;
                width: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #3f3f3f;
                min-height: 20px;
                border-radius: 5px;
            }
        """)
        
    def play_movie(self):
        """Play the movie using the system's default video player"""
        try:
            # Find the movie file
            movie_files = [f for f in self.movie_folder.iterdir() 
                         if f.suffix.lower() in {'.mp4', '.mkv', '.avi', '.mov', '.wmv'}]
            
            if movie_files:
                movie_file = movie_files[0]  # Use the first movie file found
                if sys.platform == 'win32':
                    subprocess.Popen(['start', '', str(movie_file)], shell=True)
                elif sys.platform == 'darwin':
                    subprocess.Popen(['open', str(movie_file)])
                else:
                    subprocess.Popen(['xdg-open', str(movie_file)])
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "Error", "No movie file found in the folder.")
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "Error", f"An error occurred: {e}")
            
    def keyPressEvent(self, event):
        """Handle keyboard shortcuts."""
        if event.key() == Qt.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event) 