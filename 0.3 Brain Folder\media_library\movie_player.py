from PyQt5.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
    QSlider, QLabel, QStyle, QSizePolicy
)
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget
from PyQt5.QtCore import Qt, QUrl, QTimer
from PyQt5.QtGui import QIcon, QPalette

class MoviePlayer(QWidget):
    """A video player widget with basic controls."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Media Player")
        self.setup_ui()
        
    def setup_ui(self):
        """Initialize the player UI."""
        # Set up the main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Create video widget
        self.video_widget = QVideoWidget()
        self.video_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # Set up dark theme
        palette = self.video_widget.palette()
        palette.setColor(QPalette.Window, Qt.black)
        self.video_widget.setPalette(palette)
        self.video_widget.setAutoFillBackground(True)
        
        layout.addWidget(self.video_widget)
        
        # Create media player
        self.media_player = QMediaPlayer(None, QMediaPlayer.VideoSurface)
        self.media_player.setVideoOutput(self.video_widget)
        self.media_player.stateChanged.connect(self.media_state_changed)
        self.media_player.positionChanged.connect(self.position_changed)
        self.media_player.durationChanged.connect(self.duration_changed)
        
        # Create controls widget
        controls_widget = QWidget()
        controls_widget.setStyleSheet("""
            QWidget {
                background: #202020;
                color: white;
            }
            QPushButton {
                background: transparent;
                border: none;
                color: white;
                padding: 8px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.1);
            }
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 4px;
                background: #4A4A4A;
                margin: 2px 0;
            }
            QSlider::handle:horizontal {
                background: #FFFFFF;
                border: none;
                width: 12px;
                margin: -4px 0;
                border-radius: 6px;
            }
            QLabel {
                color: white;
                padding: 0 8px;
            }
        """)
        
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(10, 5, 10, 5)
        
        # Create playback controls
        self.play_button = QPushButton()
        self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        self.play_button.clicked.connect(self.play_pause)
        
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 0)
        self.position_slider.sliderMoved.connect(self.set_position)
        
        self.duration_label = QLabel("00:00 / 00:00")
        
        controls_layout.addWidget(self.play_button)
        controls_layout.addWidget(self.position_slider)
        controls_layout.addWidget(self.duration_label)
        
        layout.addWidget(controls_widget)
        
        # Set up fullscreen handling
        self.video_widget.mouseDoubleClickEvent = self.toggle_fullscreen
        self.is_fullscreen = False
        
        # Set initial size
        self.resize(800, 600)
        
    def load_media(self, file_path):
        """Load a media file into the player."""
        self.media_player.setMedia(
            QMediaContent(QUrl.fromLocalFile(file_path))
        )
        self.play_button.setEnabled(True)
        
    def play_pause(self):
        """Toggle between play and pause states."""
        if self.media_player.state() == QMediaPlayer.PlayingState:
            self.media_player.pause()
        else:
            self.media_player.play()
            
    def play(self):
        """Start playing the media."""
        self.media_player.play()
            
    def media_state_changed(self, state):
        """Handle media state changes."""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setIcon(
                self.style().standardIcon(QStyle.SP_MediaPause)
            )
        else:
            self.play_button.setIcon(
                self.style().standardIcon(QStyle.SP_MediaPlay)
            )
            
    def position_changed(self, position):
        """Update the position slider and duration label."""
        self.position_slider.setValue(position)
        
        duration = self.media_player.duration()
        current = position
        
        current_time = self.format_time(current)
        total_time = self.format_time(duration)
        
        self.duration_label.setText(f"{current_time} / {total_time}")
        
    def duration_changed(self, duration):
        """Update the position slider's range."""
        self.position_slider.setRange(0, duration)
        
    def set_position(self, position):
        """Set the playback position."""
        self.media_player.setPosition(position)
        
    def format_time(self, ms):
        """Format milliseconds as MM:SS."""
        s = ms // 1000
        m = s // 60
        s = s % 60
        return f"{m:02d}:{s:02d}"
    
    def toggle_fullscreen(self, event):
        """Toggle fullscreen mode."""
        if self.is_fullscreen:
            self.showNormal()
        else:
            self.showFullScreen()
        self.is_fullscreen = not self.is_fullscreen
        
    def closeEvent(self, event):
        """Handle window close event."""
        self.media_player.stop()
        super().closeEvent(event) 